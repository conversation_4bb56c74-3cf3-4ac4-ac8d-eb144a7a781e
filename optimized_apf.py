"""
优化的APF算法 - 回归基础，精心调优
专注于核心APF原理，避免过度复杂化
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
import time
from apf_planner import Point, Obstacle, CircleObstacle, PolygonObstacle
from environment import Environment


@dataclass
class OptimizedAPFConfig:
    """优化APF算法配置"""
    k_att: float = 3.0  # 强引力
    k_rep: float = 80.0  # 适中斥力
    rho_0: float = 8.0   # 较小影响范围
    step_size: float = 0.6  # 较大步长
    max_iterations: int = 1000
    goal_threshold: float = 1.5
    
    # 简化的安全参数
    safety_margin: float = 0.8
    collision_check_steps: int = 5  # 减少检查步数
    
    # 简化的局部最小值处理
    escape_force: float = 40.0
    stuck_threshold: float = 0.2
    stuck_count_limit: int = 5
    
    # 最小化额外功能
    use_boundary_repulsion: bool = True
    adaptive_step_size: bool = True


class OptimizedAPFPlanner:
    """优化的APF路径规划器"""
    
    def __init__(self, map_size: Tuple[int, int] = (100, 100), config: OptimizedAPFConfig = None):
        self.map_width, self.map_height = map_size
        self.obstacles: List[Obstacle] = []
        self.config = config or OptimizedAPFConfig()
        
        # 简化的统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'total_iterations': 0
        }
    
    def add_obstacle(self, obstacle: Obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
    
    def is_point_safe(self, point: Point) -> bool:
        """简化的安全检查"""
        # 边界检查
        if (point.x < self.config.safety_margin or 
            point.x > self.map_width - self.config.safety_margin or
            point.y < self.config.safety_margin or 
            point.y > self.map_height - self.config.safety_margin):
            return False
        
        # 障碍物检查
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(point)
            if distance < self.config.safety_margin:
                return False
        
        return True
    
    def is_path_segment_safe(self, start: Point, end: Point) -> bool:
        """简化的路径段安全检查"""
        steps = self.config.collision_check_steps
        for i in range(1, steps):  # 跳过起点和终点
            t = i / steps
            check_point = Point(
                start.x + t * (end.x - start.x),
                start.y + t * (end.y - start.y)
            )
            if not self.is_point_safe(check_point):
                return False
        return True
    
    def attractive_force(self, current: Point, goal: Point) -> np.ndarray:
        """经典引力计算"""
        goal_vec = np.array([goal.x - current.x, goal.y - current.y])
        distance = np.linalg.norm(goal_vec)
        
        if distance < 1e-6:
            return np.array([0.0, 0.0])
        
        # 使用经典的抛物线-线性引力函数
        if distance <= self.config.goal_threshold:
            force_magnitude = self.config.k_att * distance
        else:
            force_magnitude = self.config.k_att * self.config.goal_threshold
        
        return force_magnitude * (goal_vec / distance)
    
    def repulsive_force(self, current: Point) -> np.ndarray:
        """经典斥力计算"""
        total_force = np.array([0.0, 0.0])
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            
            if distance > self.config.rho_0:
                continue
            
            gradient = obstacle.gradient_at_point(current)
            
            if distance < 1e-6:
                # 点在障碍物内部
                force_magnitude = self.config.k_rep * 1000
            else:
                # 经典斥力函数
                force_magnitude = self.config.k_rep * (
                    (1.0/distance - 1.0/self.config.rho_0) * (1.0/(distance**2))
                )
            
            total_force += force_magnitude * gradient
        
        # 边界斥力
        if self.config.use_boundary_repulsion:
            boundary_force = self._boundary_repulsive_force(current)
            total_force += boundary_force
        
        return total_force
    
    def _boundary_repulsive_force(self, current: Point) -> np.ndarray:
        """简化的边界斥力"""
        force = np.array([0.0, 0.0])
        boundary_influence = 3.0
        
        # 左边界
        if current.x < boundary_influence:
            force[0] += self.config.k_rep * 0.05 * (1.0/max(current.x, 0.1) - 1.0/boundary_influence)
        
        # 右边界
        if current.x > self.map_width - boundary_influence:
            dist_to_right = self.map_width - current.x
            force[0] -= self.config.k_rep * 0.05 * (1.0/max(dist_to_right, 0.1) - 1.0/boundary_influence)
        
        # 下边界
        if current.y < boundary_influence:
            force[1] += self.config.k_rep * 0.05 * (1.0/max(current.y, 0.1) - 1.0/boundary_influence)
        
        # 上边界
        if current.y > self.map_height - boundary_influence:
            dist_to_top = self.map_height - current.y
            force[1] -= self.config.k_rep * 0.05 * (1.0/max(dist_to_top, 0.1) - 1.0/boundary_influence)
        
        return force
    
    def total_force(self, current: Point, goal: Point) -> np.ndarray:
        """计算总力"""
        att_force = self.attractive_force(current, goal)
        rep_force = self.repulsive_force(current)
        return att_force + rep_force
    
    def plan_path(self, start: Point, goal: Point) -> Tuple[List[Point], bool, Dict]:
        """优化的路径规划"""
        if not self.is_point_safe(start):
            return [], False, {'error': 'Start point is not safe'}
        
        if not self.is_point_safe(goal):
            return [], False, {'error': 'Goal point is not safe'}
        
        start_time = time.time()
        path = [start]
        current = Point(start.x, start.y)
        
        stuck_count = 0
        prev_position = np.array([current.x, current.y])
        force_history = []
        
        # 重置统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'total_iterations': 0
        }
        
        for iteration in range(self.config.max_iterations):
            self.stats['total_iterations'] = iteration + 1
            
            # 检查是否到达目标
            distance_to_goal = current.distance_to(goal)
            if distance_to_goal < self.config.goal_threshold:
                path.append(goal)
                end_time = time.time()
                
                return path, True, {
                    'computation_time': end_time - start_time,
                    'path_length': self._calculate_path_length(path),
                    'iterations': iteration + 1,
                    'stats': self.stats.copy(),
                    'force_history': force_history
                }
            
            # 计算总力
            force = self.total_force(current, goal)
            force_magnitude = np.linalg.norm(force)
            force_history.append(force_magnitude)
            
            # 局部最小值检测
            if force_magnitude < 1e-6:
                self.stats['escape_attempts'] += 1
                # 简单的随机逃逸
                random_direction = np.random.normal(0, 1, 2)
                random_direction /= np.linalg.norm(random_direction)
                force += self.config.escape_force * random_direction
            
            # 限制力的大小
            max_force = 50.0
            if np.linalg.norm(force) > max_force:
                force = force / np.linalg.norm(force) * max_force
            
            # 自适应步长
            if self.config.adaptive_step_size:
                if distance_to_goal < 5.0:
                    current_step_size = self.config.step_size * max(0.3, distance_to_goal / 5.0)
                else:
                    current_step_size = self.config.step_size
            else:
                current_step_size = self.config.step_size
            
            # 计算候选新位置
            candidate_position = np.array([current.x, current.y]) + current_step_size * force
            
            # 边界约束
            candidate_position[0] = np.clip(candidate_position[0], 0, self.map_width)
            candidate_position[1] = np.clip(candidate_position[1], 0, self.map_height)
            
            candidate_point = Point(candidate_position[0], candidate_position[1])
            
            # 简化的安全性检查
            if self.is_point_safe(candidate_point):
                # 只检查终点安全性，不检查路径段
                new_position = candidate_position
                stuck_count = 0
            else:
                # 如果终点不安全，检查路径段
                if self.is_path_segment_safe(current, candidate_point):
                    new_position = candidate_position
                    stuck_count = 0
                else:
                    self.stats['collision_avoidance_count'] += 1
                    
                    # 尝试更小的步长
                    for step_scale in [0.5, 0.25]:
                        small_step_pos = np.array([current.x, current.y]) + current_step_size * step_scale * force
                        small_step_pos[0] = np.clip(small_step_pos[0], 0, self.map_width)
                        small_step_pos[1] = np.clip(small_step_pos[1], 0, self.map_height)
                        small_step_point = Point(small_step_pos[0], small_step_pos[1])
                        
                        if self.is_point_safe(small_step_point):
                            new_position = small_step_pos
                            break
                    else:
                        # 如果所有尝试都失败，保持当前位置
                        new_position = np.array([current.x, current.y])
                        stuck_count += 1
            
            # 检查是否卡住
            movement = np.linalg.norm(new_position - prev_position)
            if movement < self.config.stuck_threshold:
                stuck_count += 1
                if stuck_count > self.config.stuck_count_limit:
                    # 强制逃逸
                    self.stats['escape_attempts'] += 1
                    escape_direction = np.random.normal(0, 1, 2)
                    escape_direction /= np.linalg.norm(escape_direction)
                    
                    # 尝试在逃逸方向上找到安全位置
                    for escape_distance in [3.0, 5.0, 8.0]:
                        escape_pos = np.array([current.x, current.y]) + escape_direction * escape_distance
                        escape_pos[0] = np.clip(escape_pos[0], self.config.safety_margin, 
                                              self.map_width - self.config.safety_margin)
                        escape_pos[1] = np.clip(escape_pos[1], self.config.safety_margin, 
                                              self.map_height - self.config.safety_margin)
                        escape_point = Point(escape_pos[0], escape_pos[1])
                        
                        if self.is_point_safe(escape_point):
                            new_position = escape_pos
                            break
                    
                    stuck_count = 0
            
            current = Point(new_position[0], new_position[1])
            path.append(Point(current.x, current.y))
            prev_position = new_position
        
        # 规划失败
        end_time = time.time()
        return path, False, {
            'computation_time': end_time - start_time,
            'path_length': self._calculate_path_length(path),
            'iterations': self.config.max_iterations,
            'stats': self.stats.copy(),
            'force_history': force_history,
            'error': 'Max iterations reached',
            'final_distance': current.distance_to(goal)
        }
    
    def _calculate_path_length(self, path: List[Point]) -> float:
        """计算路径长度"""
        if len(path) < 2:
            return 0.0
        
        length = 0.0
        for i in range(1, len(path)):
            length += path[i-1].distance_to(path[i])
        
        return length


def create_simple_test_environment() -> Environment:
    """创建一个简化的测试环境"""
    env = Environment(100, 100)
    
    # 只添加几个关键障碍物
    env.add_circle_obstacle(Point(25, 25), 6)
    env.add_circle_obstacle(Point(50, 50), 5)
    env.add_circle_obstacle(Point(75, 75), 6)
    env.add_circle_obstacle(Point(30, 70), 4)
    env.add_circle_obstacle(Point(70, 30), 4)
    
    return env


def test_optimized_apf():
    """测试优化的APF算法"""
    print("=" * 60)
    print("优化APF算法测试 - 回归基础，精心调优")
    print("=" * 60)
    
    # 使用简化的环境进行测试
    env = create_simple_test_environment()
    print(f"简化环境设置: {len(env.obstacles)}个障碍物")
    
    # 创建优化规划器
    config = OptimizedAPFConfig(
        k_att=3.0,
        k_rep=80.0,
        rho_0=8.0,
        step_size=0.6,
        safety_margin=0.8,
        max_iterations=1000,
        goal_threshold=1.5
    )
    
    planner = OptimizedAPFPlanner((100, 100), config)
    planner.obstacles = env.obstacles
    
    # 测试案例
    test_cases = [
        (Point(5, 5), Point(95, 95), "对角线路径"),
        (Point(5, 95), Point(95, 5), "反对角线路径"),
        (Point(10, 50), Point(90, 50), "水平穿越"),
        (Point(50, 10), Point(50, 90), "垂直穿越"),
        (Point(15, 15), Point(85, 85), "复杂避障路径")
    ]
    
    results = []
    
    print("\n开始优化算法测试...")
    print("-" * 40)
    
    for i, (start, goal, description) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {description}")
        print(f"起点: ({start.x}, {start.y}) -> 终点: ({goal.x}, {goal.y})")
        
        # 检查起点和终点安全性
        start_safe = planner.is_point_safe(start)
        goal_safe = planner.is_point_safe(goal)
        
        print(f"起点安全: {'✓' if start_safe else '✗'}")
        print(f"终点安全: {'✓' if goal_safe else '✗'}")
        
        if not start_safe or not goal_safe:
            print("跳过此测试案例（起点或终点不安全）")
            continue
        
        # 规划路径
        path, success, info = planner.plan_path(start, goal)
        
        # 验证路径安全性
        path_safe = True
        if len(path) > 1:
            unsafe_count = 0
            for j in range(len(path)):
                if not planner.is_point_safe(path[j]):
                    unsafe_count += 1
            path_safe = unsafe_count == 0
        
        result = {
            'case': i+1,
            'description': description,
            'start': start,
            'goal': goal,
            'success': success,
            'path_safe': path_safe,
            'path_length': info['path_length'],
            'computation_time': info['computation_time'],
            'iterations': info['iterations'],
            'stats': info['stats'],
            'path': path,
            'final_distance': info.get('final_distance', 0)
        }
        results.append(result)
        
        print(f"规划结果: {'✓ 成功' if success else '✗ 失败'}")
        print(f"路径安全: {'✓ 安全' if path_safe else '✗ 不安全'}")
        print(f"路径长度: {info['path_length']:.2f}")
        print(f"计算时间: {info['computation_time']:.3f}秒")
        print(f"迭代次数: {info['iterations']}")
        print(f"碰撞避免次数: {info['stats']['collision_avoidance_count']}")
        print(f"逃逸尝试次数: {info['stats']['escape_attempts']}")
        
        if not success:
            print(f"最终距离目标: {info.get('final_distance', 0):.2f}")
    
    # 统计总结
    print("\n" + "=" * 40)
    print("优化算法测试总结")
    print("=" * 40)
    
    if results:
        success_count = sum(1 for r in results if r['success'])
        safe_count = sum(1 for r in results if r['path_safe'])
        
        print(f"测试案例总数: {len(results)}")
        print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        print(f"安全率: {safe_count}/{len(results)} ({safe_count/len(results)*100:.1f}%)")
        
        if success_count > 0:
            avg_length = np.mean([r['path_length'] for r in results if r['success']])
            print(f"平均路径长度: {avg_length:.2f}")
        
        avg_time = np.mean([r['computation_time'] for r in results])
        avg_iterations = np.mean([r['iterations'] for r in results])
        
        print(f"平均计算时间: {avg_time:.3f}秒")
        print(f"平均迭代次数: {avg_iterations:.1f}")
        
        total_escapes = sum(r['stats']['escape_attempts'] for r in results)
        print(f"总逃逸尝试次数: {total_escapes}")
        
        if success_count >= 4:
            print(f"\n🎉 优化APF算法表现优秀！")
            print(f"✓ 回归基础APF原理，避免过度复杂化")
            print(f"✓ 在简化环境中取得了良好的成功率")
            print(f"✓ 计算效率高，逃逸次数少")
    
    print("\n" + "=" * 60)
    print("优化APF测试完成！")
    print("=" * 60)
    
    return results


if __name__ == "__main__":
    results = test_optimized_apf()
