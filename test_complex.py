"""
复杂环境APF测试程序，包含计时和性能分析
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from apf_planner import APFPlanner, Point
from environment import Environment
from advanced_apf import AdvancedAPFPlanner, APFConfig

def complex_environment_test():
    """复杂环境测试"""
    print("=" * 60)
    print("复杂环境APF路径规划测试")
    print("=" * 60)
    
    # 创建复杂环境
    env = Environment(100, 100)
    
    # 添加多个圆形障碍物
    circle_obstacles = [
        (Point(20, 20), 8),
        (Point(60, 30), 6),
        (Point(40, 70), 7),
        (Point(80, 60), 5),
        (Point(30, 50), 4),
        (Point(70, 20), 5),
        (Point(25, 80), 6),
        (Point(85, 85), 4)
    ]
    
    for center, radius in circle_obstacles:
        env.add_circle_obstacle(center, radius)
    
    # 添加多边形障碍物
    # 三角形
    triangle1 = [Point(45, 40), Point(55, 50), Point(40, 55)]
    env.add_polygon_obstacle(triangle1)
    
    # 矩形
    rectangle1 = [Point(10, 60), Point(20, 60), Point(20, 75), Point(10, 75)]
    env.add_polygon_obstacle(rectangle1)
    
    # 不规则多边形
    irregular = [Point(65, 75), Point(75, 70), Point(80, 80), Point(70, 85), Point(60, 82)]
    env.add_polygon_obstacle(irregular)
    
    print(f"环境设置完成:")
    print(f"- 地图大小: {env.width}x{env.height}")
    print(f"- 圆形障碍物: {len(circle_obstacles)}个")
    print(f"- 多边形障碍物: 3个")
    print(f"- 总障碍物数: {len(env.obstacles)}个")
    
    # 测试案例
    test_cases = [
        (Point(5, 5), Point(95, 95), "对角线路径"),
        (Point(5, 95), Point(95, 5), "反对角线路径"),
        (Point(10, 50), Point(90, 50), "水平穿越"),
        (Point(50, 10), Point(50, 90), "垂直穿越"),
        (Point(15, 15), Point(85, 85), "复杂避障路径")
    ]
    
    # 1. 基础APF测试
    print("\n" + "="*40)
    print("1. 基础APF算法测试")
    print("="*40)
    
    basic_planner = APFPlanner((100, 100))
    basic_planner.obstacles = env.obstacles
    
    basic_results = []
    
    for i, (start, goal, description) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {description}")
        print(f"起点: ({start.x}, {start.y}) -> 终点: ({goal.x}, {goal.y})")
        
        start_time = time.time()
        path, success = basic_planner.plan_path(start, goal)
        end_time = time.time()
        
        computation_time = end_time - start_time
        path_length = sum(path[j-1].distance_to(path[j]) for j in range(1, len(path))) if len(path) > 1 else 0
        
        result = {
            'case': i+1,
            'description': description,
            'start': start,
            'goal': goal,
            'success': success,
            'path_length': path_length,
            'computation_time': computation_time,
            'path_points': len(path),
            'path': path
        }
        basic_results.append(result)
        
        print(f"结果: {'✓ 成功' if success else '✗ 失败'}")
        print(f"路径长度: {path_length:.2f}")
        print(f"计算时间: {computation_time:.3f}秒")
        print(f"路径点数: {len(path)}")
    
    # 2. 高级APF测试
    print("\n" + "="*40)
    print("2. 高级APF算法测试")
    print("="*40)
    
    # 创建高级配置
    config = APFConfig(
        k_att=1.0,
        k_rep=150.0,
        rho_0=8.0,
        step_size=0.4,
        adaptive_step=True,
        smooth_path=True,
        max_iterations=2000
    )
    
    advanced_planner = AdvancedAPFPlanner((100, 100), config)
    advanced_planner.obstacles = env.obstacles
    
    advanced_results = []
    
    for i, (start, goal, description) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {description}")
        print(f"起点: ({start.x}, {start.y}) -> 终点: ({goal.x}, {goal.y})")
        
        path, success, info = advanced_planner.plan_path_advanced(start, goal)
        
        result = {
            'case': i+1,
            'description': description,
            'start': start,
            'goal': goal,
            'success': success,
            'path_length': info['path_length'],
            'computation_time': info['computation_time'],
            'iterations': info['iterations'],
            'path': path
        }
        advanced_results.append(result)
        
        print(f"结果: {'✓ 成功' if success else '✗ 失败'}")
        print(f"路径长度: {info['path_length']:.2f}")
        print(f"计算时间: {info['computation_time']:.3f}秒")
        print(f"迭代次数: {info['iterations']}")
    
    # 3. 性能比较
    print("\n" + "="*40)
    print("3. 性能比较分析")
    print("="*40)
    
    basic_success_count = sum(1 for r in basic_results if r['success'])
    advanced_success_count = sum(1 for r in advanced_results if r['success'])
    
    basic_avg_time = np.mean([r['computation_time'] for r in basic_results if r['success']])
    advanced_avg_time = np.mean([r['computation_time'] for r in advanced_results if r['success']])
    
    basic_avg_length = np.mean([r['path_length'] for r in basic_results if r['success']])
    advanced_avg_length = np.mean([r['path_length'] for r in advanced_results if r['success']])
    
    print(f"基础APF:")
    print(f"  成功率: {basic_success_count}/{len(test_cases)} ({basic_success_count/len(test_cases)*100:.1f}%)")
    print(f"  平均计算时间: {basic_avg_time:.3f}秒")
    print(f"  平均路径长度: {basic_avg_length:.2f}")
    
    print(f"\n高级APF:")
    print(f"  成功率: {advanced_success_count}/{len(test_cases)} ({advanced_success_count/len(test_cases)*100:.1f}%)")
    print(f"  平均计算时间: {advanced_avg_time:.3f}秒")
    print(f"  平均路径长度: {advanced_avg_length:.2f}")
    
    if basic_success_count > 0 and advanced_success_count > 0:
        time_improvement = ((basic_avg_time - advanced_avg_time) / basic_avg_time) * 100
        length_improvement = ((basic_avg_length - advanced_avg_length) / basic_avg_length) * 100
        
        print(f"\n改进效果:")
        print(f"  计算时间: {time_improvement:+.1f}%")
        print(f"  路径长度: {length_improvement:+.1f}%")
    
    # 4. 可视化结果
    print("\n" + "="*40)
    print("4. 生成可视化结果")
    print("="*40)
    
    # 创建对比图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Complex Environment APF Path Planning Results', fontsize=16)
    
    for i in range(min(3, len(test_cases))):
        # 基础APF结果
        ax1 = axes[0, i]
        ax1.set_xlim(0, 100)
        ax1.set_ylim(0, 100)
        ax1.set_aspect('equal')
        ax1.grid(True, alpha=0.3)
        
        # 绘制障碍物
        for obstacle in env.obstacles:
            if hasattr(obstacle, 'center'):  # CircleObstacle
                circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                  obstacle.radius, color='red', alpha=0.7)
                ax1.add_patch(circle)
            else:  # PolygonObstacle
                vertices = [(v.x, v.y) for v in obstacle.vertices]
                polygon = plt.Polygon(vertices, color='red', alpha=0.7)
                ax1.add_patch(polygon)
        
        # 绘制基础APF路径
        basic_result = basic_results[i]
        ax1.plot(basic_result['start'].x, basic_result['start'].y, 'go', markersize=8)
        ax1.plot(basic_result['goal'].x, basic_result['goal'].y, 'ro', markersize=8)
        
        if basic_result['success'] and len(basic_result['path']) > 1:
            path_x = [p.x for p in basic_result['path']]
            path_y = [p.y for p in basic_result['path']]
            ax1.plot(path_x, path_y, 'b-', linewidth=2)
        
        ax1.set_title(f"Basic APF - Case {i+1}\n{basic_result['description']}\n"
                     f"{'Success' if basic_result['success'] else 'Failed'}, "
                     f"Length: {basic_result['path_length']:.1f}, "
                     f"Time: {basic_result['computation_time']:.3f}s")
        
        # 高级APF结果
        ax2 = axes[1, i]
        ax2.set_xlim(0, 100)
        ax2.set_ylim(0, 100)
        ax2.set_aspect('equal')
        ax2.grid(True, alpha=0.3)
        
        # 绘制障碍物
        for obstacle in env.obstacles:
            if hasattr(obstacle, 'center'):  # CircleObstacle
                circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                  obstacle.radius, color='red', alpha=0.7)
                ax2.add_patch(circle)
            else:  # PolygonObstacle
                vertices = [(v.x, v.y) for v in obstacle.vertices]
                polygon = plt.Polygon(vertices, color='red', alpha=0.7)
                ax2.add_patch(polygon)
        
        # 绘制高级APF路径
        advanced_result = advanced_results[i]
        ax2.plot(advanced_result['start'].x, advanced_result['start'].y, 'go', markersize=8)
        ax2.plot(advanced_result['goal'].x, advanced_result['goal'].y, 'ro', markersize=8)
        
        if advanced_result['success'] and len(advanced_result['path']) > 1:
            path_x = [p.x for p in advanced_result['path']]
            path_y = [p.y for p in advanced_result['path']]
            ax2.plot(path_x, path_y, 'g-', linewidth=2)
        
        ax2.set_title(f"Advanced APF - Case {i+1}\n{advanced_result['description']}\n"
                     f"{'Success' if advanced_result['success'] else 'Failed'}, "
                     f"Length: {advanced_result['path_length']:.1f}, "
                     f"Time: {advanced_result['computation_time']:.3f}s")
    
    plt.tight_layout()
    plt.savefig('complex_apf_comparison.png', dpi=150, bbox_inches='tight')
    print("对比结果已保存为 complex_apf_comparison.png")
    
    # 5. 统计信息
    stats = advanced_planner.get_statistics()
    print(f"\n高级APF统计信息:")
    print(f"  总规划次数: {stats['total_plans']}")
    print(f"  成功次数: {stats['successful_plans']}")
    print(f"  成功率: {stats['success_rate']:.2%}")
    print(f"  平均计算时间: {stats['avg_computation_time']:.3f}秒")
    print(f"  平均路径长度: {stats['avg_path_length']:.2f}")
    print(f"  平均迭代次数: {stats['avg_iterations']:.1f}")
    
    print("\n" + "="*60)
    print("复杂环境测试完成！")
    print("="*60)
    
    return basic_results, advanced_results

if __name__ == "__main__":
    # 设置matplotlib后端，避免中文字体警告
    plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
    
    # 运行复杂环境测试
    basic_results, advanced_results = complex_environment_test()
