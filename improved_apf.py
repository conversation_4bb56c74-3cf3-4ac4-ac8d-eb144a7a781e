"""
改进的APF算法，解决局部最小值和收敛问题
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
import time
from apf_planner import Point, Obstacle, CircleObstacle, PolygonObstacle
from environment import Environment


@dataclass
class ImprovedAPFConfig:
    """改进APF算法配置"""
    k_att: float = 1.5
    k_rep: float = 150.0
    rho_0: float = 12.0
    step_size: float = 0.3
    max_iterations: int = 2000
    goal_threshold: float = 1.0
    
    # 安全参数
    safety_margin: float = 1.2
    collision_check_steps: int = 15
    
    # 改进的局部最小值处理
    escape_force: float = 15.0
    stuck_threshold: float = 0.08
    stuck_count_limit: int = 8
    
    # 自适应参数
    adaptive_attraction: bool = True
    distance_scaling: bool = True
    
    # 路径质量控制
    max_force_magnitude: float = 25.0
    min_movement: float = 0.02
    
    # 目标导向改进
    goal_bias_factor: float = 0.3
    tangent_navigation: bool = True


class ImprovedAPFPlanner:
    """改进的APF路径规划器"""
    
    def __init__(self, map_size: Tuple[int, int] = (100, 100), config: ImprovedAPFConfig = None):
        self.map_width, self.map_height = map_size
        self.obstacles: List[Obstacle] = []
        self.config = config or ImprovedAPFConfig()
        
        # 统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'tangent_navigation_count': 0,
            'total_iterations': 0
        }
    
    def add_obstacle(self, obstacle: Obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
    
    def is_point_safe(self, point: Point) -> bool:
        """检查点是否安全"""
        if (point.x < self.config.safety_margin or 
            point.x > self.map_width - self.config.safety_margin or
            point.y < self.config.safety_margin or 
            point.y > self.map_height - self.config.safety_margin):
            return False
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(point)
            if distance < self.config.safety_margin:
                return False
        
        return True
    
    def is_path_segment_safe(self, start: Point, end: Point) -> bool:
        """检查路径段是否安全"""
        steps = self.config.collision_check_steps
        for i in range(steps + 1):
            t = i / steps
            check_point = Point(
                start.x + t * (end.x - start.x),
                start.y + t * (end.y - start.y)
            )
            if not self.is_point_safe(check_point):
                return False
        return True
    
    def adaptive_attractive_force(self, current: Point, goal: Point) -> np.ndarray:
        """自适应引力计算"""
        goal_vec = np.array([goal.x - current.x, goal.y - current.y])
        distance = np.linalg.norm(goal_vec)
        
        if distance < 1e-6:
            return np.array([0.0, 0.0])
        
        # 自适应引力强度
        if self.config.adaptive_attraction:
            # 根据距离障碍物的远近调整引力
            min_obstacle_distance = float('inf')
            for obstacle in self.obstacles:
                obs_dist = obstacle.distance_to_point(current)
                min_obstacle_distance = min(min_obstacle_distance, obs_dist)
            
            # 距离障碍物越近，引力越强，帮助脱离局部最小值
            if min_obstacle_distance < self.config.rho_0:
                attraction_boost = 1.0 + (self.config.rho_0 - min_obstacle_distance) / self.config.rho_0
                k_att_adaptive = self.config.k_att * attraction_boost
            else:
                k_att_adaptive = self.config.k_att
        else:
            k_att_adaptive = self.config.k_att
        
        # 距离缩放
        if self.config.distance_scaling:
            if distance <= self.config.goal_threshold:
                force_magnitude = k_att_adaptive * distance
            elif distance <= 20.0:
                force_magnitude = k_att_adaptive * self.config.goal_threshold
            else:
                # 对于很远的距离，使用平方根缩放
                force_magnitude = k_att_adaptive * self.config.goal_threshold * np.sqrt(20.0 / distance)
        else:
            force_magnitude = k_att_adaptive * min(distance, self.config.goal_threshold)
        
        return force_magnitude * (goal_vec / distance)
    
    def improved_repulsive_force(self, current: Point) -> np.ndarray:
        """改进的斥力计算"""
        total_force = np.array([0.0, 0.0])
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            
            if distance > self.config.rho_0:
                continue
            
            gradient = obstacle.gradient_at_point(current)
            
            if distance < self.config.safety_margin:
                if distance < 1e-6:
                    force_magnitude = self.config.k_rep * 5000
                else:
                    # 使用改进的斥力函数，避免数值爆炸
                    force_magnitude = self.config.k_rep * (
                        (1.0/max(distance, 0.1) - 1.0/self.config.safety_margin) * 
                        (1.0/max(distance**1.5, 0.01))
                    )
            else:
                # 标准斥力
                force_magnitude = self.config.k_rep * (
                    (1.0/distance - 1.0/self.config.rho_0) * (1.0/(distance**2))
                )
            
            total_force += force_magnitude * gradient
        
        # 边界斥力
        boundary_force = self._boundary_repulsive_force(current)
        total_force += boundary_force
        
        return total_force
    
    def _boundary_repulsive_force(self, current: Point) -> np.ndarray:
        """边界斥力"""
        force = np.array([0.0, 0.0])
        boundary_influence = 6.0
        
        if current.x < boundary_influence:
            force[0] += self.config.k_rep * 0.15 * (1.0/max(current.x, 0.1) - 1.0/boundary_influence)
        
        if current.x > self.map_width - boundary_influence:
            dist_to_right = self.map_width - current.x
            force[0] -= self.config.k_rep * 0.15 * (1.0/max(dist_to_right, 0.1) - 1.0/boundary_influence)
        
        if current.y < boundary_influence:
            force[1] += self.config.k_rep * 0.15 * (1.0/max(current.y, 0.1) - 1.0/boundary_influence)
        
        if current.y > self.map_height - boundary_influence:
            dist_to_top = self.map_height - current.y
            force[1] -= self.config.k_rep * 0.15 * (1.0/max(dist_to_top, 0.1) - 1.0/boundary_influence)
        
        return force
    
    def tangent_navigation_force(self, current: Point, goal: Point) -> np.ndarray:
        """切线导航力，帮助绕过障碍物"""
        if not self.config.tangent_navigation:
            return np.array([0.0, 0.0])
        
        # 找到最近的障碍物
        min_distance = float('inf')
        closest_obstacle = None
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            if distance < min_distance and distance < self.config.rho_0:
                min_distance = distance
                closest_obstacle = obstacle
        
        if closest_obstacle is None or min_distance > self.config.rho_0 * 0.8:
            return np.array([0.0, 0.0])
        
        # 计算到目标的方向
        goal_direction = np.array([goal.x - current.x, goal.y - current.y])
        goal_direction_norm = np.linalg.norm(goal_direction)
        
        if goal_direction_norm < 1e-6:
            return np.array([0.0, 0.0])
        
        goal_unit = goal_direction / goal_direction_norm
        
        # 计算障碍物梯度（斥力方向）
        obstacle_gradient = closest_obstacle.gradient_at_point(current)
        
        # 计算切线方向（垂直于梯度）
        tangent1 = np.array([-obstacle_gradient[1], obstacle_gradient[0]])
        tangent2 = np.array([obstacle_gradient[1], -obstacle_gradient[0]])
        
        # 选择更接近目标方向的切线
        if np.dot(tangent1, goal_unit) > np.dot(tangent2, goal_unit):
            tangent_direction = tangent1
        else:
            tangent_direction = tangent2
        
        # 切线力的强度与距离障碍物的远近成反比
        tangent_strength = self.config.k_att * self.config.goal_bias_factor * (
            1.0 - min_distance / self.config.rho_0
        )
        
        self.stats['tangent_navigation_count'] += 1
        return tangent_strength * tangent_direction
    
    def total_force(self, current: Point, goal: Point) -> np.ndarray:
        """计算总力"""
        att_force = self.adaptive_attractive_force(current, goal)
        rep_force = self.improved_repulsive_force(current)
        tangent_force = self.tangent_navigation_force(current, goal)
        
        return att_force + rep_force + tangent_force
    
    def find_escape_direction(self, current: Point, goal: Point) -> np.ndarray:
        """智能逃逸方向计算"""
        # 计算到目标的方向
        goal_direction = np.array([goal.x - current.x, goal.y - current.y])
        goal_direction_norm = np.linalg.norm(goal_direction)
        
        if goal_direction_norm < 1e-6:
            return np.random.normal(0, 1, 2)
        
        goal_unit = goal_direction / goal_direction_norm
        
        # 尝试几个候选方向
        candidate_directions = []
        
        # 1. 目标方向
        candidate_directions.append(goal_unit)
        
        # 2. 目标方向的左右偏移
        for angle in [np.pi/4, -np.pi/4, np.pi/2, -np.pi/2, 3*np.pi/4, -3*np.pi/4]:
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotated = np.array([
                goal_unit[0] * cos_a - goal_unit[1] * sin_a,
                goal_unit[0] * sin_a + goal_unit[1] * cos_a
            ])
            candidate_directions.append(rotated)
        
        # 3. 随机方向作为备选
        for _ in range(3):
            random_dir = np.random.normal(0, 1, 2)
            random_dir /= np.linalg.norm(random_dir)
            candidate_directions.append(random_dir)
        
        # 评估每个方向，选择最好的
        best_direction = None
        best_score = -float('inf')
        
        for direction in candidate_directions:
            # 检查这个方向上一定距离的点是否安全
            test_distance = 5.0
            test_point = Point(
                current.x + direction[0] * test_distance,
                current.y + direction[1] * test_distance
            )
            
            # 确保在边界内
            test_point.x = np.clip(test_point.x, self.config.safety_margin, 
                                 self.map_width - self.config.safety_margin)
            test_point.y = np.clip(test_point.y, self.config.safety_margin, 
                                 self.map_height - self.config.safety_margin)
            
            # 计算评分：安全性 + 朝向目标的程度
            safety_score = 1.0 if self.is_point_safe(test_point) else 0.0
            goal_alignment = np.dot(direction, goal_unit)
            
            score = safety_score * 2.0 + goal_alignment
            
            if score > best_score:
                best_score = score
                best_direction = direction
        
        return best_direction if best_direction is not None else np.random.normal(0, 1, 2)
    
    def plan_path(self, start: Point, goal: Point) -> Tuple[List[Point], bool, Dict]:
        """改进的路径规划"""
        if not self.is_point_safe(start):
            return [], False, {'error': 'Start point is not safe'}
        
        if not self.is_point_safe(goal):
            return [], False, {'error': 'Goal point is not safe'}
        
        start_time = time.time()
        path = [start]
        current = Point(start.x, start.y)
        
        stuck_count = 0
        prev_positions = []
        force_history = []
        
        # 重置统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'tangent_navigation_count': 0,
            'total_iterations': 0
        }
        
        for iteration in range(self.config.max_iterations):
            self.stats['total_iterations'] = iteration + 1
            
            # 检查是否到达目标
            distance_to_goal = current.distance_to(goal)
            if distance_to_goal < self.config.goal_threshold:
                path.append(goal)
                end_time = time.time()
                
                return path, True, {
                    'computation_time': end_time - start_time,
                    'path_length': self._calculate_path_length(path),
                    'iterations': iteration + 1,
                    'stats': self.stats.copy(),
                    'force_history': force_history
                }
            
            # 计算总力
            force = self.total_force(current, goal)
            force_magnitude = np.linalg.norm(force)
            force_history.append(force_magnitude)
            
            # 改进的局部最小值检测
            if force_magnitude < 1e-6 or stuck_count > self.config.stuck_count_limit:
                self.stats['escape_attempts'] += 1
                
                # 使用智能逃逸方向
                escape_direction = self.find_escape_direction(current, goal)
                escape_direction /= np.linalg.norm(escape_direction)
                
                force += self.config.escape_force * escape_direction
                stuck_count = 0
            
            # 限制力的大小
            if np.linalg.norm(force) > self.config.max_force_magnitude:
                force = force / np.linalg.norm(force) * self.config.max_force_magnitude
            
            # 自适应步长
            adaptive_step = self.config.step_size
            if distance_to_goal < 10.0:
                adaptive_step *= (distance_to_goal / 10.0)  # 接近目标时减小步长
            
            # 计算候选新位置
            candidate_position = np.array([current.x, current.y]) + adaptive_step * force
            
            # 边界约束
            candidate_position[0] = np.clip(candidate_position[0], 0, self.map_width)
            candidate_position[1] = np.clip(candidate_position[1], 0, self.map_height)
            
            candidate_point = Point(candidate_position[0], candidate_position[1])
            
            # 安全性检查
            if self.is_path_segment_safe(current, candidate_point):
                new_position = candidate_position
                stuck_count = 0
            else:
                self.stats['collision_avoidance_count'] += 1
                
                # 尝试更小的步长
                for step_scale in [0.5, 0.25, 0.1]:
                    small_step_pos = np.array([current.x, current.y]) + adaptive_step * step_scale * force
                    small_step_pos[0] = np.clip(small_step_pos[0], 0, self.map_width)
                    small_step_pos[1] = np.clip(small_step_pos[1], 0, self.map_height)
                    small_step_point = Point(small_step_pos[0], small_step_pos[1])
                    
                    if self.is_path_segment_safe(current, small_step_point):
                        new_position = small_step_pos
                        break
                else:
                    # 如果所有小步长都不安全，保持当前位置
                    new_position = np.array([current.x, current.y])
                    stuck_count += 1
            
            # 检查移动距离
            movement = np.linalg.norm(new_position - np.array([current.x, current.y]))
            if movement < self.config.min_movement:
                stuck_count += 1
            
            # 记录位置历史，检测循环
            prev_positions.append(new_position.copy())
            if len(prev_positions) > 8:
                prev_positions.pop(0)
                
                if len(prev_positions) >= 6:
                    recent_positions = np.array(prev_positions[-6:])
                    if np.std(recent_positions) < 0.8:
                        stuck_count += self.config.stuck_count_limit  # 强制触发逃逸
            
            current = Point(new_position[0], new_position[1])
            path.append(Point(current.x, current.y))
        
        # 规划失败
        end_time = time.time()
        return path, False, {
            'computation_time': end_time - start_time,
            'path_length': self._calculate_path_length(path),
            'iterations': self.config.max_iterations,
            'stats': self.stats.copy(),
            'force_history': force_history,
            'error': 'Max iterations reached'
        }
    
    def _calculate_path_length(self, path: List[Point]) -> float:
        """计算路径长度"""
        if len(path) < 2:
            return 0.0
        
        length = 0.0
        for i in range(1, len(path)):
            length += path[i-1].distance_to(path[i])
        
        return length
