"""
高效的人工势场法路径规划算法
支持圆形和多边形障碍物，具有鲁棒性和高效性
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Union, Optional
from dataclasses import dataclass
import time


@dataclass
class Point:
    """二维点类"""
    x: float
    y: float
    
    def __array__(self):
        return np.array([self.x, self.y])
    
    def distance_to(self, other: 'Point') -> float:
        """计算到另一点的距离"""
        return np.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)


class Obstacle:
    """障碍物基类"""
    def __init__(self, obstacle_id: int):
        self.id = obstacle_id
    
    def distance_to_point(self, point: Point) -> float:
        """计算点到障碍物的最短距离"""
        raise NotImplementedError
    
    def gradient_at_point(self, point: Point) -> np.ndarray:
        """计算点处的梯度方向（指向远离障碍物的方向）"""
        raise NotImplementedError


class CircleObstacle(Obstacle):
    """圆形障碍物"""
    def __init__(self, obstacle_id: int, center: Point, radius: float):
        super().__init__(obstacle_id)
        self.center = center
        self.radius = radius
    
    def distance_to_point(self, point: Point) -> float:
        """计算点到圆形障碍物边界的距离"""
        center_dist = point.distance_to(self.center)
        return max(0, center_dist - self.radius)
    
    def gradient_at_point(self, point: Point) -> np.ndarray:
        """计算梯度方向"""
        center_vec = np.array([point.x - self.center.x, point.y - self.center.y])
        norm = np.linalg.norm(center_vec)
        if norm < 1e-6:  # 避免除零
            return np.array([1.0, 0.0])
        return center_vec / norm


class PolygonObstacle(Obstacle):
    """多边形障碍物"""
    def __init__(self, obstacle_id: int, vertices: List[Point]):
        super().__init__(obstacle_id)
        self.vertices = vertices
        self.vertices_array = np.array([[v.x, v.y] for v in vertices])
    
    def distance_to_point(self, point: Point) -> float:
        """计算点到多边形的最短距离"""
        p = np.array([point.x, point.y])
        
        # 检查点是否在多边形内部
        if self._point_in_polygon(p):
            return 0.0
        
        # 计算到所有边的最短距离
        min_dist = float('inf')
        n = len(self.vertices_array)
        
        for i in range(n):
            v1 = self.vertices_array[i]
            v2 = self.vertices_array[(i + 1) % n]
            dist = self._point_to_line_distance(p, v1, v2)
            min_dist = min(min_dist, dist)
        
        return min_dist
    
    def gradient_at_point(self, point: Point) -> np.ndarray:
        """计算梯度方向"""
        p = np.array([point.x, point.y])
        
        # 找到最近的边
        min_dist = float('inf')
        closest_point = None
        n = len(self.vertices_array)
        
        for i in range(n):
            v1 = self.vertices_array[i]
            v2 = self.vertices_array[(i + 1) % n]
            closest_on_edge = self._closest_point_on_line(p, v1, v2)
            dist = np.linalg.norm(p - closest_on_edge)
            
            if dist < min_dist:
                min_dist = dist
                closest_point = closest_on_edge
        
        # 计算梯度方向
        if closest_point is not None:
            grad = p - closest_point
            norm = np.linalg.norm(grad)
            if norm < 1e-6:
                return np.array([1.0, 0.0])
            return grad / norm
        
        return np.array([1.0, 0.0])
    
    def _point_in_polygon(self, point: np.ndarray) -> bool:
        """使用射线法判断点是否在多边形内部"""
        x, y = point
        n = len(self.vertices_array)
        inside = False
        
        j = n - 1
        for i in range(n):
            xi, yi = self.vertices_array[i]
            xj, yj = self.vertices_array[j]
            
            if ((yi > y) != (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi):
                inside = not inside
            j = i
        
        return inside
    
    def _point_to_line_distance(self, point: np.ndarray, line_start: np.ndarray, line_end: np.ndarray) -> float:
        """计算点到线段的距离"""
        closest = self._closest_point_on_line(point, line_start, line_end)
        return np.linalg.norm(point - closest)
    
    def _closest_point_on_line(self, point: np.ndarray, line_start: np.ndarray, line_end: np.ndarray) -> np.ndarray:
        """计算点在线段上的最近点"""
        line_vec = line_end - line_start
        line_len_sq = np.dot(line_vec, line_vec)
        
        if line_len_sq < 1e-6:  # 线段长度为0
            return line_start
        
        t = max(0, min(1, np.dot(point - line_start, line_vec) / line_len_sq))
        return line_start + t * line_vec


class APFPlanner:
    """人工势场法路径规划器"""
    
    def __init__(self, map_size: Tuple[int, int] = (100, 100)):
        self.map_width, self.map_height = map_size
        self.obstacles: List[Obstacle] = []
        
        # APF参数
        self.k_att = 1.0      # 引力系数
        self.k_rep = 100.0    # 斥力系数
        self.rho_0 = 10.0     # 斥力影响范围
        self.step_size = 0.5  # 梯度下降步长
        self.max_iterations = 1000
        self.goal_threshold = 1.0
        
        # 局部最小值逃逸参数
        self.escape_force = 50.0
        self.stuck_threshold = 0.1
        self.stuck_count_limit = 10
    
    def add_obstacle(self, obstacle: Obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
    
    def set_parameters(self, k_att: float = None, k_rep: float = None,
                      rho_0: float = None, step_size: float = None):
        """设置APF参数"""
        if k_att is not None:
            self.k_att = k_att
        if k_rep is not None:
            self.k_rep = k_rep
        if rho_0 is not None:
            self.rho_0 = rho_0
        if step_size is not None:
            self.step_size = step_size

    def attractive_force(self, current: Point, goal: Point) -> np.ndarray:
        """计算引力"""
        goal_vec = np.array([goal.x - current.x, goal.y - current.y])
        distance = np.linalg.norm(goal_vec)

        if distance < 1e-6:
            return np.array([0.0, 0.0])

        # 使用抛物线型引力函数，避免距离过大时引力过强
        if distance <= self.goal_threshold:
            force_magnitude = self.k_att * distance
        else:
            force_magnitude = self.k_att * self.goal_threshold

        return force_magnitude * (goal_vec / distance)

    def repulsive_force(self, current: Point) -> np.ndarray:
        """计算斥力"""
        total_force = np.array([0.0, 0.0])

        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)

            if distance > self.rho_0:
                continue  # 超出影响范围

            if distance < 1e-6:
                # 点在障碍物内部，施加强斥力
                gradient = obstacle.gradient_at_point(current)
                force_magnitude = self.k_rep * 1000  # 很大的斥力
            else:
                gradient = obstacle.gradient_at_point(current)
                force_magnitude = self.k_rep * (1.0/distance - 1.0/self.rho_0) * (1.0/distance**2)

            total_force += force_magnitude * gradient

        # 添加边界斥力
        boundary_force = self._boundary_repulsive_force(current)
        total_force += boundary_force

        return total_force

    def _boundary_repulsive_force(self, current: Point) -> np.ndarray:
        """计算边界斥力"""
        force = np.array([0.0, 0.0])
        boundary_influence = 5.0  # 边界影响范围

        # 左边界
        if current.x < boundary_influence:
            force[0] += self.k_rep * 0.1 * (1.0/max(current.x, 0.1) - 1.0/boundary_influence)

        # 右边界
        if current.x > self.map_width - boundary_influence:
            dist_to_right = self.map_width - current.x
            force[0] -= self.k_rep * 0.1 * (1.0/max(dist_to_right, 0.1) - 1.0/boundary_influence)

        # 下边界
        if current.y < boundary_influence:
            force[1] += self.k_rep * 0.1 * (1.0/max(current.y, 0.1) - 1.0/boundary_influence)

        # 上边界
        if current.y > self.map_height - boundary_influence:
            dist_to_top = self.map_height - current.y
            force[1] -= self.k_rep * 0.1 * (1.0/max(dist_to_top, 0.1) - 1.0/boundary_influence)

        return force

    def total_force(self, current: Point, goal: Point) -> np.ndarray:
        """计算总力"""
        att_force = self.attractive_force(current, goal)
        rep_force = self.repulsive_force(current)
        return att_force + rep_force

    def plan_path(self, start: Point, goal: Point) -> Tuple[List[Point], bool]:
        """规划路径"""
        path = [start]
        current = Point(start.x, start.y)

        stuck_count = 0
        prev_position = np.array([current.x, current.y])

        for iteration in range(self.max_iterations):
            # 检查是否到达目标
            if current.distance_to(goal) < self.goal_threshold:
                path.append(goal)
                return path, True

            # 计算总力
            force = self.total_force(current, goal)

            # 检查是否陷入局部最小值
            force_magnitude = np.linalg.norm(force)
            if force_magnitude < 1e-6:
                # 添加随机扰动力
                random_force = np.random.normal(0, self.escape_force, 2)
                force += random_force

            # 限制力的大小，避免步长过大
            max_force = 50.0
            if np.linalg.norm(force) > max_force:
                force = force / np.linalg.norm(force) * max_force

            # 更新位置
            new_position = np.array([current.x, current.y]) + self.step_size * force

            # 检查边界
            new_position[0] = np.clip(new_position[0], 0, self.map_width)
            new_position[1] = np.clip(new_position[1], 0, self.map_height)

            # 检查是否卡住
            movement = np.linalg.norm(new_position - prev_position)
            if movement < self.stuck_threshold:
                stuck_count += 1
                if stuck_count > self.stuck_count_limit:
                    # 尝试逃逸
                    escape_direction = np.random.normal(0, 1, 2)
                    escape_direction /= np.linalg.norm(escape_direction)
                    new_position += escape_direction * 5.0
                    stuck_count = 0
            else:
                stuck_count = 0

            current = Point(new_position[0], new_position[1])
            path.append(Point(current.x, current.y))
            prev_position = new_position

        return path, False  # 未能到达目标
