"""
高级APF算法实现，包含性能优化和鲁棒性改进
"""

import numpy as np
from typing import List, Tuple, Optional, Dict
import time
from dataclasses import dataclass
from apf_planner import APFPlanner, Point, Obstacle
from environment import Environment


@dataclass
class APFConfig:
    """APF算法配置"""
    k_att: float = 1.0
    k_rep: float = 100.0
    rho_0: float = 10.0
    step_size: float = 0.5
    max_iterations: int = 1000
    goal_threshold: float = 1.0
    
    # 局部最小值逃逸参数
    escape_force: float = 50.0
    stuck_threshold: float = 0.1
    stuck_count_limit: int = 10
    
    # 自适应参数
    adaptive_step: bool = True
    min_step_size: float = 0.1
    max_step_size: float = 1.0
    
    # 路径平滑参数
    smooth_path: bool = True
    smooth_window: int = 5


class AdvancedAPFPlanner(APFPlanner):
    """高级APF规划器，包含多种优化技术"""
    
    def __init__(self, map_size: Tuple[int, int] = (100, 100), config: APFConfig = None):
        super().__init__(map_size)
        self.config = config or APFConfig()
        self._apply_config()
        
        # 性能统计
        self.stats = {
            'total_plans': 0,
            'successful_plans': 0,
            'avg_computation_time': 0.0,
            'avg_path_length': 0.0,
            'avg_iterations': 0.0
        }
        
        # 缓存机制
        self.distance_cache = {}
        self.gradient_cache = {}
        self.cache_enabled = True
    
    def _apply_config(self):
        """应用配置参数"""
        self.k_att = self.config.k_att
        self.k_rep = self.config.k_rep
        self.rho_0 = self.config.rho_0
        self.step_size = self.config.step_size
        self.max_iterations = self.config.max_iterations
        self.goal_threshold = self.config.goal_threshold
        self.escape_force = self.config.escape_force
        self.stuck_threshold = self.config.stuck_threshold
        self.stuck_count_limit = self.config.stuck_count_limit
    
    def clear_cache(self):
        """清空缓存"""
        self.distance_cache.clear()
        self.gradient_cache.clear()
    
    def _get_cached_distance(self, point: Point, obstacle: Obstacle) -> Optional[float]:
        """获取缓存的距离"""
        if not self.cache_enabled:
            return None
        
        key = (round(point.x, 1), round(point.y, 1), obstacle.id)
        return self.distance_cache.get(key)
    
    def _cache_distance(self, point: Point, obstacle: Obstacle, distance: float):
        """缓存距离"""
        if not self.cache_enabled:
            return
        
        key = (round(point.x, 1), round(point.y, 1), obstacle.id)
        self.distance_cache[key] = distance
    
    def repulsive_force_optimized(self, current: Point) -> np.ndarray:
        """优化的斥力计算"""
        total_force = np.array([0.0, 0.0])
        
        for obstacle in self.obstacles:
            # 尝试从缓存获取距离
            distance = self._get_cached_distance(current, obstacle)
            if distance is None:
                distance = obstacle.distance_to_point(current)
                self._cache_distance(current, obstacle, distance)
            
            if distance > self.rho_0:
                continue  # 超出影响范围
            
            if distance < 1e-6:
                # 点在障碍物内部
                gradient = obstacle.gradient_at_point(current)
                force_magnitude = self.k_rep * 1000
            else:
                gradient = obstacle.gradient_at_point(current)
                # 使用改进的斥力函数，避免数值不稳定
                force_magnitude = self.k_rep * (1.0/max(distance, 0.1) - 1.0/self.rho_0) * (1.0/max(distance**2, 0.01))
            
            total_force += force_magnitude * gradient
        
        # 添加边界斥力
        boundary_force = self._boundary_repulsive_force(current)
        total_force += boundary_force
        
        return total_force
    
    def adaptive_step_size(self, force: np.ndarray, iteration: int) -> float:
        """自适应步长调整"""
        if not self.config.adaptive_step:
            return self.step_size
        
        force_magnitude = np.linalg.norm(force)
        
        # 基于力的大小调整步长
        if force_magnitude > 100:
            step = self.config.min_step_size
        elif force_magnitude < 1:
            step = self.config.max_step_size
        else:
            # 线性插值
            ratio = (100 - force_magnitude) / 99
            step = self.config.min_step_size + ratio * (self.config.max_step_size - self.config.min_step_size)
        
        # 基于迭代次数的衰减
        decay_factor = max(0.5, 1.0 - iteration / self.max_iterations)
        step *= decay_factor
        
        return step
    
    def smooth_path(self, path: List[Point]) -> List[Point]:
        """路径平滑处理"""
        if not self.config.smooth_path or len(path) < 3:
            return path
        
        smoothed_path = [path[0]]  # 保持起点
        
        window = self.config.smooth_window
        for i in range(1, len(path) - 1):
            # 计算窗口范围
            start_idx = max(0, i - window // 2)
            end_idx = min(len(path), i + window // 2 + 1)
            
            # 计算平均位置
            avg_x = np.mean([p.x for p in path[start_idx:end_idx]])
            avg_y = np.mean([p.y for p in path[start_idx:end_idx]])
            
            smoothed_point = Point(avg_x, avg_y)
            
            # 检查平滑后的点是否有效
            if self._is_path_segment_valid(smoothed_path[-1], smoothed_point):
                smoothed_path.append(smoothed_point)
            else:
                smoothed_path.append(path[i])  # 保持原点
        
        smoothed_path.append(path[-1])  # 保持终点
        return smoothed_path
    
    def _is_path_segment_valid(self, start: Point, end: Point, num_checks: int = 10) -> bool:
        """检查路径段是否有效（不穿过障碍物）"""
        for i in range(num_checks + 1):
            t = i / num_checks
            check_point = Point(
                start.x + t * (end.x - start.x),
                start.y + t * (end.y - start.y)
            )
            
            # 检查是否与障碍物碰撞
            for obstacle in self.obstacles:
                if obstacle.distance_to_point(check_point) < 0.5:  # 安全边距
                    return False
        
        return True
    
    def plan_path_advanced(self, start: Point, goal: Point) -> Tuple[List[Point], bool, Dict]:
        """高级路径规划，包含详细统计信息"""
        start_time = time.time()
        
        path = [start]
        current = Point(start.x, start.y)
        
        stuck_count = 0
        prev_position = np.array([current.x, current.y])
        force_history = []
        
        # 清空缓存以确保新规划的准确性
        self.clear_cache()
        
        for iteration in range(self.max_iterations):
            # 检查是否到达目标
            if current.distance_to(goal) < self.goal_threshold:
                path.append(goal)
                
                # 路径平滑
                if self.config.smooth_path:
                    path = self.smooth_path(path)
                
                # 更新统计信息
                end_time = time.time()
                computation_time = end_time - start_time
                path_length = self._calculate_path_length(path)
                
                self._update_stats(True, computation_time, path_length, iteration + 1)
                
                return path, True, {
                    'computation_time': computation_time,
                    'path_length': path_length,
                    'iterations': iteration + 1,
                    'force_history': force_history
                }
            
            # 计算总力
            att_force = self.attractive_force(current, goal)
            rep_force = self.repulsive_force_optimized(current)
            total_force = att_force + rep_force
            
            force_magnitude = np.linalg.norm(total_force)
            force_history.append(force_magnitude)
            
            # 局部最小值检测和逃逸
            if force_magnitude < 1e-6:
                # 添加随机扰动力
                random_direction = np.random.normal(0, 1, 2)
                random_direction /= np.linalg.norm(random_direction)
                total_force += self.escape_force * random_direction
            
            # 自适应步长
            current_step_size = self.adaptive_step_size(total_force, iteration)
            
            # 限制力的大小
            max_force = 50.0
            if np.linalg.norm(total_force) > max_force:
                total_force = total_force / np.linalg.norm(total_force) * max_force
            
            # 更新位置
            new_position = np.array([current.x, current.y]) + current_step_size * total_force
            
            # 边界检查
            new_position[0] = np.clip(new_position[0], 0, self.map_width)
            new_position[1] = np.clip(new_position[1], 0, self.map_height)
            
            # 卡住检测
            movement = np.linalg.norm(new_position - prev_position)
            if movement < self.stuck_threshold:
                stuck_count += 1
                if stuck_count > self.stuck_count_limit:
                    # 大幅度随机跳跃
                    escape_direction = np.random.normal(0, 1, 2)
                    escape_direction /= np.linalg.norm(escape_direction)
                    new_position += escape_direction * 10.0
                    
                    # 确保跳跃后的位置在边界内
                    new_position[0] = np.clip(new_position[0], 0, self.map_width)
                    new_position[1] = np.clip(new_position[1], 0, self.map_height)
                    
                    stuck_count = 0
            else:
                stuck_count = 0
            
            current = Point(new_position[0], new_position[1])
            path.append(Point(current.x, current.y))
            prev_position = new_position
        
        # 规划失败
        end_time = time.time()
        computation_time = end_time - start_time
        path_length = self._calculate_path_length(path)
        
        self._update_stats(False, computation_time, path_length, self.max_iterations)
        
        return path, False, {
            'computation_time': computation_time,
            'path_length': path_length,
            'iterations': self.max_iterations,
            'force_history': force_history
        }
    
    def _calculate_path_length(self, path: List[Point]) -> float:
        """计算路径长度"""
        if len(path) < 2:
            return 0.0
        
        length = 0.0
        for i in range(1, len(path)):
            length += path[i-1].distance_to(path[i])
        
        return length
    
    def _update_stats(self, success: bool, computation_time: float, 
                     path_length: float, iterations: int):
        """更新统计信息"""
        self.stats['total_plans'] += 1
        if success:
            self.stats['successful_plans'] += 1
        
        # 更新平均值
        n = self.stats['total_plans']
        self.stats['avg_computation_time'] = (
            (self.stats['avg_computation_time'] * (n-1) + computation_time) / n
        )
        self.stats['avg_path_length'] = (
            (self.stats['avg_path_length'] * (n-1) + path_length) / n
        )
        self.stats['avg_iterations'] = (
            (self.stats['avg_iterations'] * (n-1) + iterations) / n
        )
    
    def get_statistics(self) -> Dict:
        """获取性能统计信息"""
        stats = self.stats.copy()
        if stats['total_plans'] > 0:
            stats['success_rate'] = stats['successful_plans'] / stats['total_plans']
        else:
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_plans': 0,
            'successful_plans': 0,
            'avg_computation_time': 0.0,
            'avg_path_length': 0.0,
            'avg_iterations': 0.0
        }
    
    def auto_tune_parameters(self, env: Environment, test_cases: List[Tuple[Point, Point]], 
                           max_evaluations: int = 50) -> APFConfig:
        """自动调参"""
        print("开始自动调参...")
        
        best_config = None
        best_score = -float('inf')
        
        for evaluation in range(max_evaluations):
            # 随机生成参数
            config = APFConfig(
                k_att=np.random.uniform(0.5, 3.0),
                k_rep=np.random.uniform(50, 300),
                rho_0=np.random.uniform(5, 20),
                step_size=np.random.uniform(0.2, 0.8),
                adaptive_step=np.random.choice([True, False]),
                smooth_path=np.random.choice([True, False])
            )
            
            # 测试配置
            self.config = config
            self._apply_config()
            self.reset_statistics()
            
            for start, goal in test_cases:
                path, success, info = self.plan_path_advanced(start, goal)
            
            # 计算评分
            stats = self.get_statistics()
            score = (stats['success_rate'] * 100 - 
                    stats['avg_computation_time'] * 10 - 
                    stats['avg_path_length'] * 0.1)
            
            if score > best_score:
                best_score = score
                best_config = config
                print(f"评估 {evaluation+1}: 新的最佳配置，评分 {score:.2f}")
        
        print(f"自动调参完成，最佳评分: {best_score:.2f}")
        return best_config
