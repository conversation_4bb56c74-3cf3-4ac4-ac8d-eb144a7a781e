"""
最简单的APF算法实现 - 大道至简
回归经典APF的核心原理，不添加任何复杂功能
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Dict
import time
from apf_planner import Point, Obstacle, CircleObstacle, PolygonObstacle
from environment import Environment


class SimpleAPFPlanner:
    """最简单的APF路径规划器"""
    
    def __init__(self, map_size: Tuple[int, int] = (100, 100)):
        self.map_width, self.map_height = map_size
        self.obstacles: List[Obstacle] = []
        
        # 经典APF参数
        self.k_att = 1.0      # 引力系数
        self.k_rep = 50.0     # 斥力系数
        self.rho_0 = 15.0     # 斥力影响范围
        self.step_size = 0.5  # 步长
        self.max_iterations = 800
        self.goal_threshold = 2.0
        
        # 局部最小值逃逸
        self.escape_force = 20.0
        self.stuck_threshold = 0.1
        self.stuck_count_limit = 10
    
    def add_obstacle(self, obstacle: Obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
    
    def attractive_force(self, current: Point, goal: Point) -> np.ndarray:
        """经典引力计算"""
        goal_vec = np.array([goal.x - current.x, goal.y - current.y])
        distance = np.linalg.norm(goal_vec)
        
        if distance < 1e-6:
            return np.array([0.0, 0.0])
        
        # 经典抛物线-线性引力函数
        if distance <= self.goal_threshold:
            force_magnitude = self.k_att * distance
        else:
            force_magnitude = self.k_att * self.goal_threshold
        
        return force_magnitude * (goal_vec / distance)
    
    def repulsive_force(self, current: Point) -> np.ndarray:
        """经典斥力计算"""
        total_force = np.array([0.0, 0.0])
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            
            if distance > self.rho_0:
                continue  # 超出影响范围
            
            if distance < 1e-6:
                # 点在障碍物内部，施加强斥力
                gradient = obstacle.gradient_at_point(current)
                force_magnitude = self.k_rep * 1000
            else:
                gradient = obstacle.gradient_at_point(current)
                # 经典斥力函数
                force_magnitude = self.k_rep * (1.0/distance - 1.0/self.rho_0) * (1.0/(distance**2))
            
            total_force += force_magnitude * gradient
        
        return total_force
    
    def total_force(self, current: Point, goal: Point) -> np.ndarray:
        """计算总力"""
        att_force = self.attractive_force(current, goal)
        rep_force = self.repulsive_force(current)
        return att_force + rep_force
    
    def plan_path(self, start: Point, goal: Point) -> Tuple[List[Point], bool, Dict]:
        """最简单的路径规划"""
        start_time = time.time()
        path = [start]
        current = Point(start.x, start.y)
        
        stuck_count = 0
        prev_position = np.array([current.x, current.y])
        
        for iteration in range(self.max_iterations):
            # 检查是否到达目标
            if current.distance_to(goal) < self.goal_threshold:
                path.append(goal)
                end_time = time.time()
                return path, True, {
                    'computation_time': end_time - start_time,
                    'path_length': self._calculate_path_length(path),
                    'iterations': iteration + 1
                }
            
            # 计算总力
            force = self.total_force(current, goal)
            
            # 检查是否陷入局部最小值
            force_magnitude = np.linalg.norm(force)
            if force_magnitude < 1e-6:
                # 添加随机扰动力
                random_force = np.random.normal(0, self.escape_force, 2)
                force += random_force
            
            # 限制力的大小，避免步长过大
            max_force = 30.0
            if np.linalg.norm(force) > max_force:
                force = force / np.linalg.norm(force) * max_force
            
            # 更新位置
            new_position = np.array([current.x, current.y]) + self.step_size * force
            
            # 检查边界
            new_position[0] = np.clip(new_position[0], 0, self.map_width)
            new_position[1] = np.clip(new_position[1], 0, self.map_height)
            
            # 检查是否卡住
            movement = np.linalg.norm(new_position - prev_position)
            if movement < self.stuck_threshold:
                stuck_count += 1
                if stuck_count > self.stuck_count_limit:
                    # 尝试逃逸
                    escape_direction = np.random.normal(0, 1, 2)
                    escape_direction /= np.linalg.norm(escape_direction)
                    new_position += escape_direction * 3.0
                    stuck_count = 0
            else:
                stuck_count = 0
            
            current = Point(new_position[0], new_position[1])
            path.append(Point(current.x, current.y))
            prev_position = new_position
        
        # 规划失败
        end_time = time.time()
        return path, False, {
            'computation_time': end_time - start_time,
            'path_length': self._calculate_path_length(path),
            'iterations': self.max_iterations,
            'final_distance': current.distance_to(goal)
        }
    
    def _calculate_path_length(self, path: List[Point]) -> float:
        """计算路径长度"""
        if len(path) < 2:
            return 0.0
        
        length = 0.0
        for i in range(1, len(path)):
            length += path[i-1].distance_to(path[i])
        
        return length


def create_basic_test_environment() -> Environment:
    """创建基础测试环境"""
    env = Environment(100, 100)
    
    # 只添加几个简单的圆形障碍物
    env.add_circle_obstacle(Point(30, 30), 5)
    env.add_circle_obstacle(Point(50, 50), 4)
    env.add_circle_obstacle(Point(70, 70), 5)
    
    return env


def test_simple_apf():
    """测试最简单的APF算法"""
    print("=" * 60)
    print("最简单APF算法测试 - 大道至简")
    print("=" * 60)
    
    # 使用基础环境
    env = create_basic_test_environment()
    print(f"基础环境设置: {len(env.obstacles)}个圆形障碍物")
    
    # 创建最简单的规划器
    planner = SimpleAPFPlanner((100, 100))
    planner.obstacles = env.obstacles
    
    # 测试案例
    test_cases = [
        (Point(10, 10), Point(90, 90), "对角线路径"),
        (Point(10, 90), Point(90, 10), "反对角线路径"),
        (Point(10, 50), Point(90, 50), "水平穿越"),
        (Point(50, 10), Point(50, 90), "垂直穿越"),
        (Point(20, 20), Point(80, 80), "避障路径")
    ]
    
    results = []
    
    print("\n开始简单算法测试...")
    print("-" * 40)
    
    for i, (start, goal, description) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {description}")
        print(f"起点: ({start.x}, {start.y}) -> 终点: ({goal.x}, {goal.y})")
        
        # 规划路径
        path, success, info = planner.plan_path(start, goal)
        
        result = {
            'case': i+1,
            'description': description,
            'start': start,
            'goal': goal,
            'success': success,
            'path_length': info['path_length'],
            'computation_time': info['computation_time'],
            'iterations': info['iterations'],
            'path': path,
            'final_distance': info.get('final_distance', 0)
        }
        results.append(result)
        
        print(f"规划结果: {'✓ 成功' if success else '✗ 失败'}")
        print(f"路径长度: {info['path_length']:.2f}")
        print(f"计算时间: {info['computation_time']:.3f}秒")
        print(f"迭代次数: {info['iterations']}")
        
        if not success:
            print(f"最终距离目标: {info.get('final_distance', 0):.2f}")
    
    # 可视化结果
    print("\n生成可视化结果...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    fig.suptitle('Simple APF Path Planning Results', fontsize=16)
    
    for i, result in enumerate(results):
        ax = axes[i]
        ax.set_xlim(0, 100)
        ax.set_ylim(0, 100)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        
        # 绘制障碍物
        for obstacle in env.obstacles:
            if hasattr(obstacle, 'center'):  # CircleObstacle
                circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                  obstacle.radius, color='red', alpha=0.7)
                ax.add_patch(circle)
        
        # 绘制路径
        start, goal = result['start'], result['goal']
        ax.plot(start.x, start.y, 'go', markersize=12, label='Start')
        ax.plot(goal.x, goal.y, 'ro', markersize=12, label='Goal')
        
        if len(result['path']) > 1:
            path_x = [p.x for p in result['path']]
            path_y = [p.y for p in result['path']]
            
            color = 'green' if result['success'] else 'blue'
            ax.plot(path_x, path_y, color=color, linewidth=2, alpha=0.8, label='Path')
            
            # 如果失败，标记最终位置
            if not result['success']:
                final_pos = result['path'][-1]
                ax.plot(final_pos.x, final_pos.y, 'x', color='orange', markersize=10, 
                       markeredgewidth=3, label='Final Position')
        
        # 设置标题
        status = "SUCCESS" if result['success'] else "FAILED"
        title_color = 'green' if result['success'] else 'red'
        
        ax.set_title(f"Case {result['case']}: {status}\n"
                    f"Length: {result['path_length']:.1f}, "
                    f"Time: {result['computation_time']:.3f}s", 
                    color=title_color, fontsize=12)
        
        if i == 0:
            ax.legend()
    
    # 隐藏最后一个子图
    axes[5].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('simple_apf_results.png', dpi=150, bbox_inches='tight')
    print("结果已保存为 simple_apf_results.png")
    
    # 统计总结
    print("\n" + "=" * 40)
    print("简单算法测试总结")
    print("=" * 40)
    
    success_count = sum(1 for r in results if r['success'])
    
    print(f"测试案例总数: {len(results)}")
    print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count > 0:
        avg_length = np.mean([r['path_length'] for r in results if r['success']])
        print(f"平均路径长度: {avg_length:.2f}")
    
    avg_time = np.mean([r['computation_time'] for r in results])
    avg_iterations = np.mean([r['iterations'] for r in results])
    
    print(f"平均计算时间: {avg_time:.3f}秒")
    print(f"平均迭代次数: {avg_iterations:.1f}")
    
    print(f"\n算法特点:")
    print(f"✓ 最简单的经典APF实现")
    print(f"✓ 只有引力场和斥力场")
    print(f"✓ 基础的局部最小值逃逸")
    print(f"✓ 无复杂的安全检查和切线导航")
    print(f"✓ 计算速度快")
    
    if success_count >= 3:
        print(f"\n🎉 简单APF算法表现良好！")
        print(f"证明了'大道至简'的道理")
    
    print("\n" + "=" * 60)
    print("简单APF测试完成！")
    print("=" * 60)
    
    return results


if __name__ == "__main__":
    # 设置matplotlib
    plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
    
    # 运行测试
    results = test_simple_apf()
