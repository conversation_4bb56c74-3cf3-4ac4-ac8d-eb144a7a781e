"""
测试平衡的APF算法
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from balanced_apf import BalancedAPFPlanner, BalancedAPFConfig
from apf_planner import Point
from environment import Environment

def test_balanced_apf():
    """测试平衡的APF算法"""
    print("=" * 60)
    print("平衡APF算法测试 - 在安全性和目标导向性之间取得平衡")
    print("=" * 60)
    
    # 创建相同的复杂环境
    env = Environment(100, 100)
    
    # 添加障碍物
    circle_obstacles = [
        (Point(20, 20), 8),
        (Point(60, 30), 6),
        (Point(40, 70), 7),
        (Point(80, 60), 5),
        (Point(30, 50), 4),
        (Point(70, 20), 5),
        (Point(25, 80), 6),
        (Point(85, 85), 4)
    ]
    
    for center, radius in circle_obstacles:
        env.add_circle_obstacle(center, radius)
    
    # 添加多边形障碍物
    triangle1 = [Point(45, 40), Point(55, 50), Point(40, 55)]
    env.add_polygon_obstacle(triangle1)
    
    rectangle1 = [Point(10, 60), Point(20, 60), Point(20, 75), Point(10, 75)]
    env.add_polygon_obstacle(rectangle1)
    
    irregular = [Point(65, 75), Point(75, 70), Point(80, 80), Point(70, 85), Point(60, 82)]
    env.add_polygon_obstacle(irregular)
    
    print(f"环境设置: {len(env.obstacles)}个障碍物")
    
    # 创建平衡规划器
    config = BalancedAPFConfig(
        k_att=2.5,
        k_rep=100.0,
        rho_0=10.0,
        step_size=0.5,
        safety_margin=1.0,
        max_iterations=1500,
        goal_bias_factor=0.2,
        tangent_activation_threshold=0.6,
        progress_check_interval=100,
        min_progress_rate=0.02
    )
    
    planner = BalancedAPFPlanner((100, 100), config)
    planner.obstacles = env.obstacles
    
    # 测试案例
    test_cases = [
        (Point(5, 5), Point(95, 95), "对角线路径"),
        (Point(5, 95), Point(95, 5), "反对角线路径 (关键测试)"),
        (Point(10, 50), Point(90, 50), "水平穿越"),
        (Point(50, 10), Point(50, 90), "垂直穿越"),
        (Point(15, 15), Point(85, 85), "复杂避障路径")
    ]
    
    results = []
    
    print("\n开始平衡算法测试...")
    print("-" * 40)
    
    for i, (start, goal, description) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {description}")
        print(f"起点: ({start.x}, {start.y}) -> 终点: ({goal.x}, {goal.y})")
        
        # 检查起点和终点安全性
        start_safe = planner.is_point_safe(start)
        goal_safe = planner.is_point_safe(goal)
        
        print(f"起点安全: {'✓' if start_safe else '✗'}")
        print(f"终点安全: {'✓' if goal_safe else '✗'}")
        
        if not start_safe or not goal_safe:
            print("跳过此测试案例（起点或终点不安全）")
            continue
        
        # 规划路径
        path, success, info = planner.plan_path(start, goal)
        
        # 验证路径安全性
        path_safe = True
        if len(path) > 1:
            for j in range(len(path)):
                if not planner.is_point_safe(path[j]):
                    path_safe = False
                    break
            
            if path_safe:
                for j in range(1, len(path)):
                    if not planner.is_path_segment_safe(path[j-1], path[j]):
                        path_safe = False
                        break
        
        result = {
            'case': i+1,
            'description': description,
            'start': start,
            'goal': goal,
            'success': success,
            'path_safe': path_safe,
            'path_length': info['path_length'],
            'computation_time': info['computation_time'],
            'iterations': info['iterations'],
            'stats': info['stats'],
            'path': path,
            'final_distance': info.get('final_distance', 0)
        }
        results.append(result)
        
        print(f"规划结果: {'✓ 成功' if success else '✗ 失败'}")
        print(f"路径安全: {'✓ 安全' if path_safe else '✗ 不安全'}")
        print(f"路径长度: {info['path_length']:.2f}")
        print(f"计算时间: {info['computation_time']:.3f}秒")
        print(f"迭代次数: {info['iterations']}")
        print(f"碰撞避免次数: {info['stats']['collision_avoidance_count']}")
        print(f"逃逸尝试次数: {info['stats']['escape_attempts']}")
        print(f"切线导航次数: {info['stats']['tangent_navigation_count']}")
        print(f"进度停滞次数: {info['stats']['progress_stagnations']}")
        
        if not success:
            print(f"最终距离目标: {info.get('final_distance', 0):.2f}")
    
    # 生成可视化结果
    print("\n生成可视化结果...")
    
    n_cases = len(results)
    if n_cases > 0:
        cols = min(3, n_cases)
        rows = (n_cases + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 6*rows))
        if n_cases == 1:
            axes = [axes]
        elif rows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()
        
        fig.suptitle('Balanced APF Path Planning Results', fontsize=16)
        
        for i, result in enumerate(results):
            ax = axes[i]
            ax.set_xlim(0, 100)
            ax.set_ylim(0, 100)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            
            # 绘制障碍物
            for obstacle in env.obstacles:
                if hasattr(obstacle, 'center'):  # CircleObstacle
                    circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                      obstacle.radius, color='red', alpha=0.7)
                    ax.add_patch(circle)
                    # 安全边距
                    safety_circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                             obstacle.radius + config.safety_margin, 
                                             color='red', alpha=0.15, linestyle='--', fill=False)
                    ax.add_patch(safety_circle)
                else:  # PolygonObstacle
                    vertices = [(v.x, v.y) for v in obstacle.vertices]
                    polygon = plt.Polygon(vertices, color='red', alpha=0.7)
                    ax.add_patch(polygon)
            
            # 绘制路径
            start, goal = result['start'], result['goal']
            ax.plot(start.x, start.y, 'go', markersize=12, label='Start')
            ax.plot(goal.x, goal.y, 'ro', markersize=12, label='Goal')
            
            if len(result['path']) > 1:
                path_x = [p.x for p in result['path']]
                path_y = [p.y for p in result['path']]
                
                # 根据成功与否选择颜色
                if result['success']:
                    color = 'green'
                    alpha = 1.0
                else:
                    color = 'blue'
                    alpha = 0.7
                
                ax.plot(path_x, path_y, color=color, linewidth=2.5, alpha=alpha, label='Path')
                ax.plot(path_x, path_y, 'o', color=color, markersize=1.5, alpha=0.6)
                
                # 如果失败，标记最终位置
                if not result['success']:
                    final_pos = result['path'][-1]
                    ax.plot(final_pos.x, final_pos.y, 'x', color='orange', markersize=12, 
                           markeredgewidth=3, label='Final Position')
            
            # 设置标题
            if result['success']:
                title_color = 'green'
                title = f"Case {result['case']}: SUCCESS\n" \
                       f"Length: {result['path_length']:.1f}, " \
                       f"Time: {result['computation_time']:.2f}s\n" \
                       f"Tangent Nav: {result['stats']['tangent_navigation_count']}"
            else:
                title_color = 'red'
                title = f"Case {result['case']}: FAILED\n" \
                       f"Final Dist: {result['final_distance']:.1f}, " \
                       f"Time: {result['computation_time']:.2f}s\n" \
                       f"Tangent Nav: {result['stats']['tangent_navigation_count']}"
            
            ax.set_title(title, color=title_color, fontsize=10)
            
            if i == 0:
                ax.legend(fontsize=8)
        
        # 隐藏多余的子图
        for i in range(n_cases, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('balanced_apf_results.png', dpi=150, bbox_inches='tight')
        print("结果已保存为 balanced_apf_results.png")
    
    # 统计总结
    print("\n" + "=" * 40)
    print("平衡算法测试总结")
    print("=" * 40)
    
    if results:
        success_count = sum(1 for r in results if r['success'])
        safe_count = sum(1 for r in results if r['path_safe'])
        
        avg_length = np.mean([r['path_length'] for r in results if r['success']]) if success_count > 0 else 0
        avg_time = np.mean([r['computation_time'] for r in results])
        avg_iterations = np.mean([r['iterations'] for r in results])
        
        total_escapes = sum(r['stats']['escape_attempts'] for r in results)
        total_tangent = sum(r['stats']['tangent_navigation_count'] for r in results)
        total_stagnations = sum(r['stats']['progress_stagnations'] for r in results)
        
        print(f"测试案例总数: {len(results)}")
        print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        print(f"安全率: {safe_count}/{len(results)} ({safe_count/len(results)*100:.1f}%)")
        if success_count > 0:
            print(f"平均路径长度: {avg_length:.2f}")
        print(f"平均计算时间: {avg_time:.3f}秒")
        print(f"平均迭代次数: {avg_iterations:.1f}")
        print(f"总逃逸尝试次数: {total_escapes}")
        print(f"总切线导航次数: {total_tangent}")
        print(f"总进度停滞次数: {total_stagnations}")
        
        # 重点关注Case 2
        case2_result = None
        for r in results:
            if "反对角线" in r['description']:
                case2_result = r
                break
        
        print(f"\n关键案例分析 - 反对角线路径:")
        if case2_result:
            print(f"结果: {'✓ 成功' if case2_result['success'] else '✗ 失败'}")
            if case2_result['success']:
                print(f"✓ 路径长度: {case2_result['path_length']:.2f}")
                print(f"✓ 计算时间: {case2_result['computation_time']:.3f}秒")
                print(f"✓ 切线导航次数: {case2_result['stats']['tangent_navigation_count']}")
            else:
                print(f"✗ 最终距离: {case2_result['final_distance']:.2f}")
                print(f"✗ 切线导航次数: {case2_result['stats']['tangent_navigation_count']}")
                print(f"✗ 进度停滞次数: {case2_result['stats']['progress_stagnations']}")
        
        # 算法演进对比
        print(f"\n算法演进对比:")
        print(f"版本对比        | 成功率 | 切线导航 | 主要特点")
        print(f"原始APF         | 50%   | 0       | 基础实现")
        print(f"鲁棒APF         | 50%   | 0       | 安全性改进")
        print(f"改进APF         | 75%   | 2824    | 智能切线导航")
        print(f"最终APF         | 50%   | 7000+   | 过度切线导航")
        print(f"平衡APF         | {success_count/len(results)*100:.0f}%   | {total_tangent}     | 平衡安全与效率")
        
        if success_count >= 3:
            print(f"\n🎉 平衡APF算法表现优秀！")
            print(f"✓ 成功解决了复杂环境路径规划问题")
            print(f"✓ 在安全性和目标导向性之间取得了良好平衡")
            print(f"✓ 切线导航次数得到有效控制")
    
    print("\n" + "=" * 60)
    print("平衡APF测试完成！")
    print("=" * 60)
    
    return results

if __name__ == "__main__":
    # 设置matplotlib
    plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
    
    # 运行测试
    results = test_balanced_apf()
