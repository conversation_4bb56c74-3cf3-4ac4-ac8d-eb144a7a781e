"""
地图环境类和障碍物管理
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional
from apf_planner import Point, Obstacle, CircleObstacle, PolygonObstacle


class Environment:
    """地图环境类"""
    
    def __init__(self, width: int = 100, height: int = 100):
        self.width = width
        self.height = height
        self.obstacles: List[Obstacle] = []
        self.obstacle_counter = 0
    
    def add_circle_obstacle(self, center: Point, radius: float) -> int:
        """添加圆形障碍物"""
        obstacle = CircleObstacle(self.obstacle_counter, center, radius)
        self.obstacles.append(obstacle)
        self.obstacle_counter += 1
        return obstacle.id
    
    def add_polygon_obstacle(self, vertices: List[Point]) -> int:
        """添加多边形障碍物"""
        if len(vertices) < 3:
            raise ValueError("多边形至少需要3个顶点")
        
        obstacle = PolygonObstacle(self.obstacle_counter, vertices)
        self.obstacles.append(obstacle)
        self.obstacle_counter += 1
        return obstacle.id
    
    def remove_obstacle(self, obstacle_id: int) -> bool:
        """移除障碍物"""
        for i, obstacle in enumerate(self.obstacles):
            if obstacle.id == obstacle_id:
                del self.obstacles[i]
                return True
        return False
    
    def is_point_valid(self, point: Point, safety_margin: float = 0.5) -> bool:
        """检查点是否有效（不在障碍物内且有安全边距）"""
        # 检查边界
        if (point.x < safety_margin or point.x > self.width - safety_margin or
            point.y < safety_margin or point.y > self.height - safety_margin):
            return False
        
        # 检查障碍物
        for obstacle in self.obstacles:
            if obstacle.distance_to_point(point) < safety_margin:
                return False
        
        return True
    
    def get_random_valid_point(self, safety_margin: float = 0.5, max_attempts: int = 1000) -> Optional[Point]:
        """获取随机有效点"""
        for _ in range(max_attempts):
            x = np.random.uniform(safety_margin, self.width - safety_margin)
            y = np.random.uniform(safety_margin, self.height - safety_margin)
            point = Point(x, y)
            
            if self.is_point_valid(point, safety_margin):
                return point
        
        return None
    
    def create_sample_environment(self) -> None:
        """创建示例环境"""
        # 添加一些圆形障碍物
        self.add_circle_obstacle(Point(20, 20), 8)
        self.add_circle_obstacle(Point(60, 30), 6)
        self.add_circle_obstacle(Point(40, 70), 7)
        self.add_circle_obstacle(Point(80, 60), 5)
        
        # 添加一些多边形障碍物
        # 三角形
        triangle = [Point(30, 40), Point(40, 50), Point(25, 55)]
        self.add_polygon_obstacle(triangle)
        
        # 矩形
        rectangle = [Point(65, 70), Point(85, 70), Point(85, 85), Point(65, 85)]
        self.add_polygon_obstacle(rectangle)
        
        # 不规则多边形
        irregular = [Point(10, 80), Point(25, 75), Point(30, 85), Point(20, 95), Point(5, 90)]
        self.add_polygon_obstacle(irregular)
    
    def visualize(self, path: List[Point] = None, start: Point = None, goal: Point = None, 
                 title: str = "APF路径规划", figsize: Tuple[int, int] = (10, 10)):
        """可视化环境和路径"""
        fig, ax = plt.subplots(figsize=figsize)
        
        # 设置坐标轴
        ax.set_xlim(0, self.width)
        ax.set_ylim(0, self.height)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_title(title)
        
        # 绘制障碍物
        for obstacle in self.obstacles:
            if isinstance(obstacle, CircleObstacle):
                circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                  obstacle.radius, color='red', alpha=0.7)
                ax.add_patch(circle)
            elif isinstance(obstacle, PolygonObstacle):
                vertices = [(v.x, v.y) for v in obstacle.vertices]
                polygon = plt.Polygon(vertices, color='red', alpha=0.7)
                ax.add_patch(polygon)
        
        # 绘制起点和终点
        if start:
            ax.plot(start.x, start.y, 'go', markersize=10, label='起点')
        if goal:
            ax.plot(goal.x, goal.y, 'ro', markersize=10, label='终点')
        
        # 绘制路径
        if path and len(path) > 1:
            path_x = [p.x for p in path]
            path_y = [p.y for p in path]
            ax.plot(path_x, path_y, 'b-', linewidth=2, label='规划路径')
            ax.plot(path_x, path_y, 'b.', markersize=3)
        
        if start or goal or path:
            ax.legend()
        
        plt.tight_layout()
        return fig, ax
    
    def create_complex_environment(self) -> None:
        """创建复杂测试环境"""
        # 创建迷宫式环境
        # 外围障碍物
        self.add_circle_obstacle(Point(15, 15), 5)
        self.add_circle_obstacle(Point(85, 15), 5)
        self.add_circle_obstacle(Point(15, 85), 5)
        self.add_circle_obstacle(Point(85, 85), 5)
        
        # 中央障碍物群
        self.add_circle_obstacle(Point(50, 50), 8)
        self.add_circle_obstacle(Point(35, 50), 4)
        self.add_circle_obstacle(Point(65, 50), 4)
        self.add_circle_obstacle(Point(50, 35), 4)
        self.add_circle_obstacle(Point(50, 65), 4)
        
        # 通道障碍物
        corridor_obstacles = [
            Point(25, 30), Point(75, 30), Point(25, 70), Point(75, 70),
            Point(30, 25), Point(70, 25), Point(30, 75), Point(70, 75)
        ]
        for center in corridor_obstacles:
            self.add_circle_obstacle(center, 3)
        
        # 多边形障碍物
        # L形障碍物
        l_shape = [Point(10, 40), Point(25, 40), Point(25, 45), Point(15, 45), 
                  Point(15, 60), Point(10, 60)]
        self.add_polygon_obstacle(l_shape)
        
        # 另一个L形障碍物
        l_shape2 = [Point(75, 40), Point(90, 40), Point(90, 60), Point(85, 60), 
                   Point(85, 45), Point(75, 45)]
        self.add_polygon_obstacle(l_shape2)
    
    def get_obstacle_density_map(self, resolution: int = 100) -> np.ndarray:
        """获取障碍物密度图，用于分析环境复杂度"""
        density_map = np.zeros((resolution, resolution))
        
        for i in range(resolution):
            for j in range(resolution):
                x = (i / resolution) * self.width
                y = (j / resolution) * self.height
                point = Point(x, y)
                
                min_distance = float('inf')
                for obstacle in self.obstacles:
                    distance = obstacle.distance_to_point(point)
                    min_distance = min(min_distance, distance)
                
                # 将距离转换为密度值
                if min_distance == 0:
                    density_map[j, i] = 1.0  # 在障碍物内
                else:
                    density_map[j, i] = max(0, 1.0 - min_distance / 10.0)
        
        return density_map
