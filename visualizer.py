"""
高级可视化功能
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle, Polygon
from typing import List, Tuple, Optional
import time
from apf_planner import Point, APFPlanner, CircleObstacle, PolygonObstacle
from environment import Environment


class APFVisualizer:
    """APF算法可视化器"""
    
    def __init__(self, environment: Environment, planner: APFPlanner):
        self.env = environment
        self.planner = planner
        self.fig = None
        self.ax = None
    
    def visualize_potential_field(self, goal: Point, resolution: int = 50, 
                                figsize: Tuple[int, int] = (12, 10)):
        """可视化势场分布"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=figsize)
        
        # 创建网格
        x = np.linspace(0, self.env.width, resolution)
        y = np.linspace(0, self.env.height, resolution)
        X, Y = np.meshgrid(x, y)
        
        # 计算势场
        attractive_field = np.zeros_like(X)
        repulsive_field = np.zeros_like(X)
        total_field = np.zeros_like(X)
        
        for i in range(resolution):
            for j in range(resolution):
                point = Point(X[i, j], Y[i, j])
                
                # 引力场
                att_force = self.planner.attractive_force(point, goal)
                attractive_field[i, j] = np.linalg.norm(att_force)
                
                # 斥力场
                rep_force = self.planner.repulsive_force(point)
                repulsive_field[i, j] = np.linalg.norm(rep_force)
                
                # 总势场
                total_force = self.planner.total_force(point, goal)
                total_field[i, j] = np.linalg.norm(total_force)
        
        # 绘制引力场
        im1 = ax1.contourf(X, Y, attractive_field, levels=20, cmap='Blues')
        ax1.set_title('引力场')
        ax1.set_aspect('equal')
        plt.colorbar(im1, ax=ax1)
        self._draw_obstacles(ax1)
        ax1.plot(goal.x, goal.y, 'r*', markersize=15, label='目标点')
        ax1.legend()
        
        # 绘制斥力场
        im2 = ax2.contourf(X, Y, repulsive_field, levels=20, cmap='Reds')
        ax2.set_title('斥力场')
        ax2.set_aspect('equal')
        plt.colorbar(im2, ax=ax2)
        self._draw_obstacles(ax2)
        
        # 绘制总势场
        im3 = ax3.contourf(X, Y, total_field, levels=20, cmap='viridis')
        ax3.set_title('总势场')
        ax3.set_aspect('equal')
        plt.colorbar(im3, ax=ax3)
        self._draw_obstacles(ax3)
        ax3.plot(goal.x, goal.y, 'r*', markersize=15, label='目标点')
        ax3.legend()
        
        # 绘制势场梯度
        skip = 3  # 跳过一些箭头以避免过于密集
        ax4.quiver(X[::skip, ::skip], Y[::skip, ::skip], 
                  -np.gradient(total_field, axis=1)[::skip, ::skip],
                  -np.gradient(total_field, axis=0)[::skip, ::skip],
                  alpha=0.7)
        ax4.set_title('势场梯度（力的方向）')
        ax4.set_aspect('equal')
        self._draw_obstacles(ax4)
        ax4.plot(goal.x, goal.y, 'r*', markersize=15, label='目标点')
        ax4.legend()
        
        plt.tight_layout()
        return fig
    
    def _draw_obstacles(self, ax):
        """在给定的轴上绘制障碍物"""
        for obstacle in self.env.obstacles:
            if isinstance(obstacle, CircleObstacle):
                circle = Circle((obstacle.center.x, obstacle.center.y), 
                              obstacle.radius, color='red', alpha=0.7)
                ax.add_patch(circle)
            elif isinstance(obstacle, PolygonObstacle):
                vertices = [(v.x, v.y) for v in obstacle.vertices]
                polygon = Polygon(vertices, color='red', alpha=0.7)
                ax.add_patch(polygon)
    
    def animate_path_planning(self, start: Point, goal: Point, 
                            save_path: str = None) -> animation.FuncAnimation:
        """动画显示路径规划过程"""
        # 获取完整路径
        path, success = self.planner.plan_path(start, goal)
        
        if not success:
            print("路径规划失败！")
            return None
        
        # 设置图形
        self.fig, self.ax = plt.subplots(figsize=(10, 10))
        self.ax.set_xlim(0, self.env.width)
        self.ax.set_ylim(0, self.env.height)
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_title('APF路径规划动画')
        
        # 绘制环境
        self._draw_obstacles(self.ax)
        self.ax.plot(start.x, start.y, 'go', markersize=12, label='起点')
        self.ax.plot(goal.x, goal.y, 'ro', markersize=12, label='终点')
        
        # 初始化路径线
        self.path_line, = self.ax.plot([], [], 'b-', linewidth=2, label='路径')
        self.current_point, = self.ax.plot([], [], 'bo', markersize=8, label='当前位置')
        
        self.ax.legend()
        
        def animate(frame):
            if frame < len(path):
                # 更新路径
                path_x = [p.x for p in path[:frame+1]]
                path_y = [p.y for p in path[:frame+1]]
                self.path_line.set_data(path_x, path_y)
                
                # 更新当前点
                current = path[frame]
                self.current_point.set_data([current.x], [current.y])
            
            return self.path_line, self.current_point
        
        anim = animation.FuncAnimation(self.fig, animate, frames=len(path), 
                                     interval=50, blit=True, repeat=True)
        
        if save_path:
            anim.save(save_path, writer='pillow', fps=20)
        
        return anim
    
    def compare_parameters(self, start: Point, goal: Point, 
                         parameter_sets: List[dict], figsize: Tuple[int, int] = (15, 10)):
        """比较不同参数设置的效果"""
        n_sets = len(parameter_sets)
        cols = min(3, n_sets)
        rows = (n_sets + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=figsize)
        if n_sets == 1:
            axes = [axes]
        elif rows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()
        
        results = []
        
        for i, params in enumerate(parameter_sets):
            # 设置参数
            self.planner.set_parameters(**params)
            
            # 规划路径
            start_time = time.time()
            path, success = self.planner.plan_path(start, goal)
            end_time = time.time()
            
            # 计算统计信息
            path_length = 0
            if len(path) > 1:
                for j in range(1, len(path)):
                    path_length += path[j-1].distance_to(path[j])
            
            results.append({
                'params': params,
                'success': success,
                'path_length': path_length,
                'computation_time': end_time - start_time,
                'path_points': len(path)
            })
            
            # 绘制结果
            ax = axes[i]
            ax.set_xlim(0, self.env.width)
            ax.set_ylim(0, self.env.height)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            
            # 绘制环境
            self._draw_obstacles(ax)
            ax.plot(start.x, start.y, 'go', markersize=10)
            ax.plot(goal.x, goal.y, 'ro', markersize=10)
            
            # 绘制路径
            if success and len(path) > 1:
                path_x = [p.x for p in path]
                path_y = [p.y for p in path]
                ax.plot(path_x, path_y, 'b-', linewidth=2)
                color = 'green'
            else:
                color = 'red'
            
            # 设置标题
            title = f"参数组 {i+1}\n"
            for key, value in params.items():
                title += f"{key}={value:.1f} "
            title += f"\n长度:{path_length:.1f}, 时间:{end_time-start_time:.3f}s"
            ax.set_title(title, color=color, fontsize=10)
        
        # 隐藏多余的子图
        for i in range(n_sets, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        return fig, results
    
    def plot_convergence_analysis(self, start: Point, goal: Point, 
                                figsize: Tuple[int, int] = (12, 8)):
        """分析算法收敛性"""
        # 记录规划过程中的信息
        original_plan_path = self.planner.plan_path
        
        distances_to_goal = []
        force_magnitudes = []
        positions = []
        
        def tracking_plan_path(start_point, goal_point):
            path = [start_point]
            current = Point(start_point.x, start_point.y)
            
            for iteration in range(self.planner.max_iterations):
                # 记录信息
                distance_to_goal = current.distance_to(goal_point)
                distances_to_goal.append(distance_to_goal)
                positions.append((current.x, current.y))
                
                if distance_to_goal < self.planner.goal_threshold:
                    path.append(goal_point)
                    return path, True
                
                force = self.planner.total_force(current, goal_point)
                force_magnitude = np.linalg.norm(force)
                force_magnitudes.append(force_magnitude)
                
                if force_magnitude < 1e-6:
                    random_force = np.random.normal(0, self.planner.escape_force, 2)
                    force += random_force
                
                max_force = 50.0
                if np.linalg.norm(force) > max_force:
                    force = force / np.linalg.norm(force) * max_force
                
                new_position = np.array([current.x, current.y]) + self.planner.step_size * force
                new_position[0] = np.clip(new_position[0], 0, self.planner.map_width)
                new_position[1] = np.clip(new_position[1], 0, self.planner.map_height)
                
                current = Point(new_position[0], new_position[1])
                path.append(Point(current.x, current.y))
            
            return path, False
        
        # 临时替换规划函数
        self.planner.plan_path = tracking_plan_path
        
        # 执行规划
        path, success = self.planner.plan_path(start, goal)
        
        # 恢复原函数
        self.planner.plan_path = original_plan_path
        
        # 绘制分析结果
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=figsize)
        
        # 距离收敛图
        ax1.plot(distances_to_goal)
        ax1.set_title('到目标点的距离收敛')
        ax1.set_xlabel('迭代次数')
        ax1.set_ylabel('距离')
        ax1.grid(True)
        
        # 力大小变化图
        ax2.plot(force_magnitudes)
        ax2.set_title('合力大小变化')
        ax2.set_xlabel('迭代次数')
        ax2.set_ylabel('力大小')
        ax2.grid(True)
        
        # 路径轨迹
        ax3.set_xlim(0, self.env.width)
        ax3.set_ylim(0, self.env.height)
        ax3.set_aspect('equal')
        self._draw_obstacles(ax3)
        ax3.plot(start.x, start.y, 'go', markersize=10, label='起点')
        ax3.plot(goal.x, goal.y, 'ro', markersize=10, label='终点')
        
        if len(path) > 1:
            path_x = [p.x for p in path]
            path_y = [p.y for p in path]
            ax3.plot(path_x, path_y, 'b-', linewidth=2, label='路径')
        ax3.set_title('规划路径')
        ax3.legend()
        
        # 位置热力图
        if positions:
            pos_x, pos_y = zip(*positions)
            ax4.hist2d(pos_x, pos_y, bins=20, cmap='hot')
            ax4.set_title('位置访问热力图')
            ax4.set_xlabel('X')
            ax4.set_ylabel('Y')
        
        plt.tight_layout()
        return fig, {
            'success': success,
            'iterations': len(distances_to_goal),
            'final_distance': distances_to_goal[-1] if distances_to_goal else float('inf'),
            'path_length': sum(path[i-1].distance_to(path[i]) for i in range(1, len(path)))
        }
