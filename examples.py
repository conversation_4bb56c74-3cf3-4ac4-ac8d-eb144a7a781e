"""
APF路径规划算法使用示例和测试
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from typing import List, Tuple

from apf_planner import APFPlanner, Point
from environment import Environment
from visualizer import APFVisualizer


def basic_example():
    """基础使用示例"""
    print("=== 基础APF路径规划示例 ===")
    
    # 创建环境
    env = Environment(100, 100)
    env.create_sample_environment()
    
    # 创建规划器
    planner = APFPlanner((100, 100))
    planner.obstacles = env.obstacles
    
    # 设置起点和终点
    start = Point(10, 10)
    goal = Point(90, 90)
    
    # 确保起点和终点有效
    if not env.is_point_valid(start):
        print("起点无效，重新选择...")
        start = env.get_random_valid_point()
    
    if not env.is_point_valid(goal):
        print("终点无效，重新选择...")
        goal = env.get_random_valid_point()
    
    print(f"起点: ({start.x:.1f}, {start.y:.1f})")
    print(f"终点: ({goal.x:.1f}, {goal.y:.1f})")
    
    # 规划路径
    start_time = time.time()
    path, success = planner.plan_path(start, goal)
    end_time = time.time()
    
    # 计算路径长度
    path_length = 0
    if len(path) > 1:
        for i in range(1, len(path)):
            path_length += path[i-1].distance_to(path[i])
    
    print(f"规划结果: {'成功' if success else '失败'}")
    print(f"路径长度: {path_length:.2f}")
    print(f"路径点数: {len(path)}")
    print(f"计算时间: {end_time - start_time:.3f}秒")
    
    # 可视化
    fig, ax = env.visualize(path, start, goal, "基础APF路径规划示例")
    plt.show()
    
    return path, success


def parameter_comparison_example():
    """参数比较示例"""
    print("\n=== 参数比较示例 ===")
    
    # 创建环境
    env = Environment(100, 100)
    env.create_sample_environment()
    
    # 创建规划器
    planner = APFPlanner((100, 100))
    planner.obstacles = env.obstacles
    
    # 设置起点和终点
    start = Point(10, 10)
    goal = Point(90, 90)
    
    # 不同的参数组合
    parameter_sets = [
        {'k_att': 1.0, 'k_rep': 50.0, 'rho_0': 15.0, 'step_size': 0.5},
        {'k_att': 2.0, 'k_rep': 100.0, 'rho_0': 10.0, 'step_size': 0.3},
        {'k_att': 0.5, 'k_rep': 200.0, 'rho_0': 8.0, 'step_size': 0.7},
        {'k_att': 1.5, 'k_rep': 150.0, 'rho_0': 12.0, 'step_size': 0.4}
    ]
    
    # 创建可视化器
    visualizer = APFVisualizer(env, planner)
    
    # 比较参数
    fig, results = visualizer.compare_parameters(start, goal, parameter_sets)
    plt.show()
    
    # 打印结果
    print("参数比较结果:")
    for i, result in enumerate(results):
        print(f"参数组 {i+1}: 成功={result['success']}, "
              f"路径长度={result['path_length']:.2f}, "
              f"计算时间={result['computation_time']:.3f}秒")
    
    return results


def complex_environment_example():
    """复杂环境示例"""
    print("\n=== 复杂环境路径规划示例 ===")
    
    # 创建复杂环境
    env = Environment(100, 100)
    env.create_complex_environment()
    
    # 创建规划器
    planner = APFPlanner((100, 100))
    planner.obstacles = env.obstacles
    
    # 设置更好的参数用于复杂环境
    planner.set_parameters(k_att=1.0, k_rep=150.0, rho_0=8.0, step_size=0.3)
    planner.max_iterations = 2000
    
    # 尝试多个起点终点组合
    test_cases = [
        (Point(5, 5), Point(95, 95)),
        (Point(5, 95), Point(95, 5)),
        (Point(25, 5), Point(75, 95)),
        (Point(5, 50), Point(95, 50))
    ]
    
    results = []
    
    for i, (start, goal) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: ({start.x}, {start.y}) -> ({goal.x}, {goal.y})")
        
        start_time = time.time()
        path, success = planner.plan_path(start, goal)
        end_time = time.time()
        
        path_length = 0
        if len(path) > 1:
            for j in range(1, len(path)):
                path_length += path[j-1].distance_to(path[j])
        
        result = {
            'case': i+1,
            'start': start,
            'goal': goal,
            'success': success,
            'path_length': path_length,
            'computation_time': end_time - start_time,
            'path': path
        }
        results.append(result)
        
        print(f"结果: {'成功' if success else '失败'}, "
              f"长度: {path_length:.2f}, 时间: {end_time - start_time:.3f}秒")
    
    # 可视化所有结果
    fig, axes = plt.subplots(2, 2, figsize=(15, 15))
    axes = axes.flatten()
    
    for i, result in enumerate(results):
        ax = axes[i]
        ax.set_xlim(0, 100)
        ax.set_ylim(0, 100)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        
        # 绘制障碍物
        for obstacle in env.obstacles:
            if hasattr(obstacle, 'center'):  # CircleObstacle
                circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                  obstacle.radius, color='red', alpha=0.7)
                ax.add_patch(circle)
            else:  # PolygonObstacle
                vertices = [(v.x, v.y) for v in obstacle.vertices]
                polygon = plt.Polygon(vertices, color='red', alpha=0.7)
                ax.add_patch(polygon)
        
        # 绘制路径
        start, goal = result['start'], result['goal']
        ax.plot(start.x, start.y, 'go', markersize=10)
        ax.plot(goal.x, goal.y, 'ro', markersize=10)
        
        if result['success'] and len(result['path']) > 1:
            path_x = [p.x for p in result['path']]
            path_y = [p.y for p in result['path']]
            ax.plot(path_x, path_y, 'b-', linewidth=2)
        
        ax.set_title(f"案例 {result['case']}: {'成功' if result['success'] else '失败'}\n"
                    f"长度: {result['path_length']:.1f}, "
                    f"时间: {result['computation_time']:.3f}s")
    
    plt.tight_layout()
    plt.show()
    
    return results


def performance_benchmark():
    """性能基准测试"""
    print("\n=== 性能基准测试 ===")
    
    # 测试不同环境复杂度下的性能
    complexities = [
        ("简单", lambda env: env.create_sample_environment()),
        ("复杂", lambda env: env.create_complex_environment())
    ]
    
    results = {}
    
    for complexity_name, setup_func in complexities:
        print(f"\n测试 {complexity_name} 环境:")
        
        # 创建环境
        env = Environment(100, 100)
        setup_func(env)
        
        # 创建规划器
        planner = APFPlanner((100, 100))
        planner.obstacles = env.obstacles
        
        # 生成随机测试案例
        test_times = []
        success_count = 0
        total_tests = 20
        
        for i in range(total_tests):
            # 生成随机起点和终点
            start = env.get_random_valid_point()
            goal = env.get_random_valid_point()
            
            if start is None or goal is None:
                continue
            
            # 确保起点和终点距离足够远
            if start.distance_to(goal) < 20:
                continue
            
            start_time = time.time()
            path, success = planner.plan_path(start, goal)
            end_time = time.time()
            
            test_times.append(end_time - start_time)
            if success:
                success_count += 1
        
        if test_times:
            avg_time = np.mean(test_times)
            std_time = np.std(test_times)
            success_rate = success_count / len(test_times)
            
            results[complexity_name] = {
                'avg_time': avg_time,
                'std_time': std_time,
                'success_rate': success_rate,
                'tests_run': len(test_times)
            }
            
            print(f"平均计算时间: {avg_time:.3f} ± {std_time:.3f} 秒")
            print(f"成功率: {success_rate:.2%}")
            print(f"测试次数: {len(test_times)}")
    
    return results


def potential_field_visualization_example():
    """势场可视化示例"""
    print("\n=== 势场可视化示例 ===")
    
    # 创建简单环境用于清晰展示势场
    env = Environment(100, 100)
    env.add_circle_obstacle(Point(30, 30), 8)
    env.add_circle_obstacle(Point(70, 70), 6)
    
    # 添加一个多边形障碍物
    triangle = [Point(50, 20), Point(60, 40), Point(40, 40)]
    env.add_polygon_obstacle(triangle)
    
    # 创建规划器
    planner = APFPlanner((100, 100))
    planner.obstacles = env.obstacles
    
    # 创建可视化器
    visualizer = APFVisualizer(env, planner)
    
    # 设置目标点
    goal = Point(80, 80)
    
    # 可视化势场
    fig = visualizer.visualize_potential_field(goal, resolution=50)
    plt.show()
    
    return fig


if __name__ == "__main__":
    # 运行所有示例
    print("开始运行APF路径规划示例...")
    
    # 基础示例
    basic_example()
    
    # 参数比较
    parameter_comparison_example()
    
    # 复杂环境测试
    complex_environment_example()
    
    # 性能测试
    performance_benchmark()
    
    # 势场可视化
    potential_field_visualization_example()
    
    print("\n所有示例运行完成！")
