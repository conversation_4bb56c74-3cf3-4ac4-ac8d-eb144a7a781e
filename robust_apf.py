"""
鲁棒的APF算法实现，修复穿越障碍物和往返折线问题
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
import time
from apf_planner import Point, Obstacle, CircleObstacle, PolygonObstacle
from environment import Environment


@dataclass
class RobustAPFConfig:
    """鲁棒APF算法配置"""
    k_att: float = 1.0
    k_rep: float = 200.0  # 增强斥力
    rho_0: float = 15.0   # 增大影响范围
    step_size: float = 0.2  # 减小步长
    max_iterations: int = 2000
    goal_threshold: float = 1.0
    
    # 安全参数
    safety_margin: float = 1.0  # 安全边距
    collision_check_steps: int = 20  # 碰撞检测步数
    
    # 局部最小值逃逸参数
    escape_force: float = 20.0  # 减小逃逸力
    stuck_threshold: float = 0.05
    stuck_count_limit: int = 15
    
    # 路径质量控制
    max_force_magnitude: float = 30.0
    min_movement: float = 0.01


class RobustAPFPlanner:
    """鲁棒的APF路径规划器"""
    
    def __init__(self, map_size: Tuple[int, int] = (100, 100), config: RobustAPFConfig = None):
        self.map_width, self.map_height = map_size
        self.obstacles: List[Obstacle] = []
        self.config = config or RobustAPFConfig()
        
        # 统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'total_iterations': 0
        }
    
    def add_obstacle(self, obstacle: Obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
    
    def is_point_safe(self, point: Point) -> bool:
        """检查点是否安全（不在障碍物内且有安全边距）"""
        # 检查边界
        if (point.x < self.config.safety_margin or 
            point.x > self.map_width - self.config.safety_margin or
            point.y < self.config.safety_margin or 
            point.y > self.map_height - self.config.safety_margin):
            return False
        
        # 检查障碍物
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(point)
            if distance < self.config.safety_margin:
                return False
        
        return True
    
    def is_path_segment_safe(self, start: Point, end: Point) -> bool:
        """检查路径段是否安全"""
        steps = self.config.collision_check_steps
        for i in range(steps + 1):
            t = i / steps
            check_point = Point(
                start.x + t * (end.x - start.x),
                start.y + t * (end.y - start.y)
            )
            if not self.is_point_safe(check_point):
                return False
        return True
    
    def attractive_force(self, current: Point, goal: Point) -> np.ndarray:
        """计算引力"""
        goal_vec = np.array([goal.x - current.x, goal.y - current.y])
        distance = np.linalg.norm(goal_vec)
        
        if distance < 1e-6:
            return np.array([0.0, 0.0])
        
        # 使用改进的引力函数
        if distance <= self.config.goal_threshold:
            force_magnitude = self.config.k_att * distance
        else:
            # 对于远距离，使用线性引力避免过强
            force_magnitude = self.config.k_att * self.config.goal_threshold
        
        return force_magnitude * (goal_vec / distance)
    
    def repulsive_force(self, current: Point) -> np.ndarray:
        """计算改进的斥力"""
        total_force = np.array([0.0, 0.0])
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            
            # 扩大影响范围
            if distance > self.config.rho_0:
                continue
            
            gradient = obstacle.gradient_at_point(current)
            
            if distance < self.config.safety_margin:
                # 在安全边距内，施加强斥力
                if distance < 1e-6:
                    force_magnitude = self.config.k_rep * 10000  # 极强斥力
                else:
                    # 使用指数型斥力函数，距离越近斥力越强
                    force_magnitude = self.config.k_rep * np.exp(-distance * 2) / max(distance, 0.01)
            else:
                # 标准斥力计算
                force_magnitude = self.config.k_rep * (1.0/distance - 1.0/self.config.rho_0) * (1.0/(distance**2))
            
            total_force += force_magnitude * gradient
        
        # 添加边界斥力
        boundary_force = self._boundary_repulsive_force(current)
        total_force += boundary_force
        
        return total_force
    
    def _boundary_repulsive_force(self, current: Point) -> np.ndarray:
        """计算边界斥力"""
        force = np.array([0.0, 0.0])
        boundary_influence = 8.0  # 增大边界影响范围
        
        # 左边界
        if current.x < boundary_influence:
            force[0] += self.config.k_rep * 0.2 * (1.0/max(current.x, 0.1) - 1.0/boundary_influence)
        
        # 右边界
        if current.x > self.map_width - boundary_influence:
            dist_to_right = self.map_width - current.x
            force[0] -= self.config.k_rep * 0.2 * (1.0/max(dist_to_right, 0.1) - 1.0/boundary_influence)
        
        # 下边界
        if current.y < boundary_influence:
            force[1] += self.config.k_rep * 0.2 * (1.0/max(current.y, 0.1) - 1.0/boundary_influence)
        
        # 上边界
        if current.y > self.map_height - boundary_influence:
            dist_to_top = self.map_height - current.y
            force[1] -= self.config.k_rep * 0.2 * (1.0/max(dist_to_top, 0.1) - 1.0/boundary_influence)
        
        return force
    
    def total_force(self, current: Point, goal: Point) -> np.ndarray:
        """计算总力"""
        att_force = self.attractive_force(current, goal)
        rep_force = self.repulsive_force(current)
        return att_force + rep_force
    
    def find_safe_position(self, current: Point, direction: np.ndarray, max_distance: float = 5.0) -> Point:
        """在给定方向上寻找安全位置"""
        direction_norm = np.linalg.norm(direction)
        if direction_norm < 1e-6:
            return current
        
        unit_direction = direction / direction_norm
        
        # 逐步搜索安全位置
        for distance in np.linspace(0.1, max_distance, 50):
            candidate = Point(
                current.x + unit_direction[0] * distance,
                current.y + unit_direction[1] * distance
            )
            
            # 确保在边界内
            candidate.x = np.clip(candidate.x, self.config.safety_margin, 
                                self.map_width - self.config.safety_margin)
            candidate.y = np.clip(candidate.y, self.config.safety_margin, 
                                self.map_height - self.config.safety_margin)
            
            if self.is_point_safe(candidate):
                return candidate
        
        return current  # 如果找不到安全位置，返回当前位置
    
    def plan_path(self, start: Point, goal: Point) -> Tuple[List[Point], bool, Dict]:
        """鲁棒路径规划"""
        if not self.is_point_safe(start):
            return [], False, {'error': 'Start point is not safe'}
        
        if not self.is_point_safe(goal):
            return [], False, {'error': 'Goal point is not safe'}
        
        start_time = time.time()
        path = [start]
        current = Point(start.x, start.y)
        
        stuck_count = 0
        prev_positions = []  # 记录最近几个位置
        force_history = []
        
        # 重置统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'total_iterations': 0
        }
        
        for iteration in range(self.config.max_iterations):
            self.stats['total_iterations'] = iteration + 1
            
            # 检查是否到达目标
            if current.distance_to(goal) < self.config.goal_threshold:
                path.append(goal)
                end_time = time.time()
                
                return path, True, {
                    'computation_time': end_time - start_time,
                    'path_length': self._calculate_path_length(path),
                    'iterations': iteration + 1,
                    'stats': self.stats.copy(),
                    'force_history': force_history
                }
            
            # 计算总力
            force = self.total_force(current, goal)
            force_magnitude = np.linalg.norm(force)
            force_history.append(force_magnitude)
            
            # 局部最小值检测
            if force_magnitude < 1e-6:
                self.stats['escape_attempts'] += 1
                # 温和的随机扰动
                random_direction = np.random.normal(0, 1, 2)
                random_direction /= np.linalg.norm(random_direction)
                force += self.config.escape_force * random_direction
            
            # 限制力的大小
            if np.linalg.norm(force) > self.config.max_force_magnitude:
                force = force / np.linalg.norm(force) * self.config.max_force_magnitude
            
            # 计算候选新位置
            candidate_position = np.array([current.x, current.y]) + self.config.step_size * force
            
            # 边界约束
            candidate_position[0] = np.clip(candidate_position[0], 0, self.map_width)
            candidate_position[1] = np.clip(candidate_position[1], 0, self.map_height)
            
            candidate_point = Point(candidate_position[0], candidate_position[1])
            
            # 安全性检查
            if self.is_path_segment_safe(current, candidate_point):
                # 路径安全，正常移动
                new_position = candidate_position
                stuck_count = 0
            else:
                # 路径不安全，寻找安全位置
                self.stats['collision_avoidance_count'] += 1
                
                # 尝试沿着切线方向移动
                rep_force = self.repulsive_force(current)
                if np.linalg.norm(rep_force) > 1e-6:
                    # 计算切线方向（垂直于斥力方向）
                    rep_unit = rep_force / np.linalg.norm(rep_force)
                    tangent_direction = np.array([-rep_unit[1], rep_unit[0]])
                    
                    # 选择朝向目标的切线方向
                    goal_direction = np.array([goal.x - current.x, goal.y - current.y])
                    if np.dot(tangent_direction, goal_direction) < 0:
                        tangent_direction = -tangent_direction
                    
                    safe_point = self.find_safe_position(current, tangent_direction, 2.0)
                    new_position = np.array([safe_point.x, safe_point.y])
                else:
                    # 如果无法计算切线，尝试小步移动
                    small_step = self.config.step_size * 0.1
                    new_position = np.array([current.x, current.y]) + small_step * force
                    new_position[0] = np.clip(new_position[0], 0, self.map_width)
                    new_position[1] = np.clip(new_position[1], 0, self.map_height)
            
            # 检查移动距离
            movement = np.linalg.norm(new_position - np.array([current.x, current.y]))
            if movement < self.config.min_movement:
                stuck_count += 1
                if stuck_count > self.config.stuck_count_limit:
                    # 尝试更大的逃逸
                    self.stats['escape_attempts'] += 1
                    escape_direction = np.random.normal(0, 1, 2)
                    escape_direction /= np.linalg.norm(escape_direction)
                    
                    # 寻找逃逸位置
                    escape_point = self.find_safe_position(current, escape_direction, 8.0)
                    new_position = np.array([escape_point.x, escape_point.y])
                    stuck_count = 0
            
            # 检查是否在循环
            prev_positions.append(new_position.copy())
            if len(prev_positions) > 10:
                prev_positions.pop(0)
                
                # 检查是否在小范围内循环
                if len(prev_positions) >= 6:
                    recent_positions = np.array(prev_positions[-6:])
                    if np.std(recent_positions) < 1.0:  # 位置变化很小
                        # 强制跳出循环
                        self.stats['escape_attempts'] += 1
                        random_direction = np.random.normal(0, 1, 2)
                        random_direction /= np.linalg.norm(random_direction)
                        escape_point = self.find_safe_position(current, random_direction, 10.0)
                        new_position = np.array([escape_point.x, escape_point.y])
                        prev_positions.clear()
            
            current = Point(new_position[0], new_position[1])
            path.append(Point(current.x, current.y))
        
        # 规划失败
        end_time = time.time()
        return path, False, {
            'computation_time': end_time - start_time,
            'path_length': self._calculate_path_length(path),
            'iterations': self.config.max_iterations,
            'stats': self.stats.copy(),
            'force_history': force_history,
            'error': 'Max iterations reached'
        }
    
    def _calculate_path_length(self, path: List[Point]) -> float:
        """计算路径长度"""
        if len(path) < 2:
            return 0.0
        
        length = 0.0
        for i in range(1, len(path)):
            length += path[i-1].distance_to(path[i])
        
        return length
