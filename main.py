"""
APF路径规划系统主程序
演示完整的功能和使用方法
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from typing import List, Tuple

from apf_planner import APFPlanner, Point
from environment import Environment
from visualizer import APFVisualizer
from advanced_apf import AdvancedAPFPlanner, APFConfig
from examples import *


def main_demo():
    """主演示程序"""
    print("=" * 60)
    print("高效人工势场法路径规划系统")
    print("支持圆形和多边形障碍物，具有鲁棒性和高效性")
    print("=" * 60)
    
    # 1. 基础功能演示
    print("\n1. 基础功能演示")
    print("-" * 30)
    
    # 创建环境
    env = Environment(100, 100)
    env.create_sample_environment()
    
    # 创建基础规划器
    basic_planner = APFPlanner((100, 100))
    basic_planner.obstacles = env.obstacles
    
    # 设置起点和终点
    start = Point(10, 10)
    goal = Point(90, 90)
    
    print(f"起点: ({start.x}, {start.y})")
    print(f"终点: ({goal.x}, {goal.y})")
    print(f"环境中有 {len(env.obstacles)} 个障碍物")
    
    # 基础路径规划
    start_time = time.time()
    path, success = basic_planner.plan_path(start, goal)
    end_time = time.time()
    
    path_length = sum(path[i-1].distance_to(path[i]) for i in range(1, len(path)))
    
    print(f"基础APF结果: {'成功' if success else '失败'}")
    print(f"路径长度: {path_length:.2f}")
    print(f"计算时间: {end_time - start_time:.3f}秒")
    print(f"路径点数: {len(path)}")
    
    # 可视化基础结果
    fig1, ax1 = env.visualize(path, start, goal, "基础APF路径规划")
    plt.show()
    
    # 2. 高级功能演示
    print("\n2. 高级功能演示")
    print("-" * 30)
    
    # 创建高级规划器
    config = APFConfig(
        k_att=1.0,
        k_rep=150.0,
        rho_0=10.0,
        step_size=0.4,
        adaptive_step=True,
        smooth_path=True,
        max_iterations=1500
    )
    
    advanced_planner = AdvancedAPFPlanner((100, 100), config)
    advanced_planner.obstacles = env.obstacles
    
    # 高级路径规划
    path_adv, success_adv, info = advanced_planner.plan_path_advanced(start, goal)
    
    print(f"高级APF结果: {'成功' if success_adv else '失败'}")
    print(f"路径长度: {info['path_length']:.2f}")
    print(f"计算时间: {info['computation_time']:.3f}秒")
    print(f"迭代次数: {info['iterations']}")
    
    # 比较结果
    print(f"\n改进效果:")
    print(f"路径长度减少: {((path_length - info['path_length']) / path_length * 100):.1f}%")
    print(f"计算时间变化: {((info['computation_time'] - (end_time - start_time)) / (end_time - start_time) * 100):+.1f}%")
    
    # 可视化高级结果
    fig2, ax2 = env.visualize(path_adv, start, goal, "高级APF路径规划（优化后）")
    plt.show()
    
    # 3. 势场可视化
    print("\n3. 势场可视化")
    print("-" * 30)
    
    visualizer = APFVisualizer(env, advanced_planner)
    fig3 = visualizer.visualize_potential_field(goal, resolution=40)
    plt.show()
    
    # 4. 复杂环境测试
    print("\n4. 复杂环境测试")
    print("-" * 30)
    
    # 创建复杂环境
    complex_env = Environment(100, 100)
    complex_env.create_complex_environment()
    
    advanced_planner.obstacles = complex_env.obstacles
    
    # 测试多个案例
    test_cases = [
        (Point(5, 5), Point(95, 95)),
        (Point(5, 95), Point(95, 5)),
        (Point(25, 5), Point(75, 95))
    ]
    
    complex_results = []
    
    for i, (test_start, test_goal) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: ({test_start.x}, {test_start.y}) -> ({test_goal.x}, {test_goal.y})")
        
        path_complex, success_complex, info_complex = advanced_planner.plan_path_advanced(test_start, test_goal)
        
        complex_results.append((path_complex, success_complex, info_complex))
        
        print(f"结果: {'成功' if success_complex else '失败'}")
        print(f"路径长度: {info_complex['path_length']:.2f}")
        print(f"计算时间: {info_complex['computation_time']:.3f}秒")
        print(f"迭代次数: {info_complex['iterations']}")
    
    # 可视化复杂环境结果
    fig4, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for i, ((path_c, success_c, info_c), (test_start, test_goal)) in enumerate(zip(complex_results, test_cases)):
        ax = axes[i]
        ax.set_xlim(0, 100)
        ax.set_ylim(0, 100)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        
        # 绘制障碍物
        for obstacle in complex_env.obstacles:
            if hasattr(obstacle, 'center'):  # CircleObstacle
                circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                  obstacle.radius, color='red', alpha=0.7)
                ax.add_patch(circle)
            else:  # PolygonObstacle
                vertices = [(v.x, v.y) for v in obstacle.vertices]
                polygon = plt.Polygon(vertices, color='red', alpha=0.7)
                ax.add_patch(polygon)
        
        # 绘制路径
        ax.plot(test_start.x, test_start.y, 'go', markersize=10, label='起点')
        ax.plot(test_goal.x, test_goal.y, 'ro', markersize=10, label='终点')
        
        if success_c and len(path_c) > 1:
            path_x = [p.x for p in path_c]
            path_y = [p.y for p in path_c]
            ax.plot(path_x, path_y, 'b-', linewidth=2, label='路径')
        
        ax.set_title(f"案例 {i+1}: {'成功' if success_c else '失败'}\n"
                    f"长度: {info_c['path_length']:.1f}, 时间: {info_c['computation_time']:.3f}s")
        ax.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 5. 性能统计
    print("\n5. 性能统计")
    print("-" * 30)
    
    stats = advanced_planner.get_statistics()
    print(f"总规划次数: {stats['total_plans']}")
    print(f"成功次数: {stats['successful_plans']}")
    print(f"成功率: {stats['success_rate']:.2%}")
    print(f"平均计算时间: {stats['avg_computation_time']:.3f}秒")
    print(f"平均路径长度: {stats['avg_path_length']:.2f}")
    print(f"平均迭代次数: {stats['avg_iterations']:.1f}")
    
    # 6. 参数比较
    print("\n6. 参数比较")
    print("-" * 30)
    
    # 重置环境为简单环境进行参数比较
    advanced_planner.obstacles = env.obstacles
    
    parameter_sets = [
        {'k_att': 1.0, 'k_rep': 100.0, 'rho_0': 10.0, 'step_size': 0.5},
        {'k_att': 1.5, 'k_rep': 150.0, 'rho_0': 8.0, 'step_size': 0.3},
        {'k_att': 0.8, 'k_rep': 200.0, 'rho_0': 12.0, 'step_size': 0.4}
    ]
    
    visualizer_basic = APFVisualizer(env, basic_planner)
    fig5, param_results = visualizer_basic.compare_parameters(start, goal, parameter_sets)
    plt.show()
    
    print("参数比较结果:")
    for i, result in enumerate(param_results):
        print(f"参数组 {i+1}: 成功={result['success']}, "
              f"路径长度={result['path_length']:.2f}, "
              f"计算时间={result['computation_time']:.3f}秒")
    
    print("\n=" * 60)
    print("演示完成！")
    print("该APF路径规划系统具有以下特点：")
    print("✓ 支持圆形和多边形障碍物")
    print("✓ 高效的势场计算和缓存机制")
    print("✓ 自适应步长和路径平滑")
    print("✓ 局部最小值逃逸机制")
    print("✓ 完整的可视化和分析工具")
    print("✓ 鲁棒性强，适用于复杂环境")
    print("=" * 60)


if __name__ == "__main__":
    # 设置matplotlib中文字体（如果需要）
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 运行主演示
    main_demo()
