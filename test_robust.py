"""
测试鲁棒APF算法，验证修复效果
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from robust_apf import RobustAPFPlanner, RobustAPFConfig
from apf_planner import Point
from environment import Environment

def test_robust_apf():
    """测试鲁棒APF算法"""
    print("=" * 60)
    print("鲁棒APF算法测试 - 修复穿越障碍物和往返折线问题")
    print("=" * 60)
    
    # 创建复杂环境（与之前相同）
    env = Environment(100, 100)
    
    # 添加障碍物
    circle_obstacles = [
        (Point(20, 20), 8),
        (Point(60, 30), 6),
        (Point(40, 70), 7),
        (Point(80, 60), 5),
        (Point(30, 50), 4),
        (Point(70, 20), 5),
        (Point(25, 80), 6),
        (Point(85, 85), 4)
    ]
    
    for center, radius in circle_obstacles:
        env.add_circle_obstacle(center, radius)
    
    # 添加多边形障碍物
    triangle1 = [Point(45, 40), Point(55, 50), Point(40, 55)]
    env.add_polygon_obstacle(triangle1)
    
    rectangle1 = [Point(10, 60), Point(20, 60), Point(20, 75), Point(10, 75)]
    env.add_polygon_obstacle(rectangle1)
    
    irregular = [Point(65, 75), Point(75, 70), Point(80, 80), Point(70, 85), Point(60, 82)]
    env.add_polygon_obstacle(irregular)
    
    print(f"环境设置: {len(env.obstacles)}个障碍物")
    
    # 创建鲁棒规划器
    config = RobustAPFConfig(
        k_att=1.0,
        k_rep=200.0,
        rho_0=15.0,
        step_size=0.2,
        safety_margin=1.5,
        max_iterations=3000
    )
    
    planner = RobustAPFPlanner((100, 100), config)
    planner.obstacles = env.obstacles
    
    # 测试案例
    test_cases = [
        (Point(5, 5), Point(95, 95), "对角线路径"),
        (Point(5, 95), Point(95, 5), "反对角线路径"),
        (Point(10, 50), Point(90, 50), "水平穿越"),
        (Point(50, 10), Point(50, 90), "垂直穿越"),
        (Point(15, 15), Point(85, 85), "复杂避障路径")
    ]
    
    results = []
    
    print("\n开始路径规划测试...")
    print("-" * 40)
    
    for i, (start, goal, description) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {description}")
        print(f"起点: ({start.x}, {start.y}) -> 终点: ({goal.x}, {goal.y})")
        
        # 检查起点和终点安全性
        start_safe = planner.is_point_safe(start)
        goal_safe = planner.is_point_safe(goal)
        
        print(f"起点安全: {'✓' if start_safe else '✗'}")
        print(f"终点安全: {'✓' if goal_safe else '✗'}")
        
        if not start_safe or not goal_safe:
            print("跳过此测试案例（起点或终点不安全）")
            continue
        
        # 规划路径
        path, success, info = planner.plan_path(start, goal)
        
        # 验证路径安全性
        path_safe = True
        if success and len(path) > 1:
            for j in range(len(path)):
                if not planner.is_point_safe(path[j]):
                    path_safe = False
                    break
            
            # 检查路径段安全性
            if path_safe:
                for j in range(1, len(path)):
                    if not planner.is_path_segment_safe(path[j-1], path[j]):
                        path_safe = False
                        break
        
        result = {
            'case': i+1,
            'description': description,
            'start': start,
            'goal': goal,
            'success': success,
            'path_safe': path_safe,
            'path_length': info['path_length'],
            'computation_time': info['computation_time'],
            'iterations': info['iterations'],
            'stats': info['stats'],
            'path': path
        }
        results.append(result)
        
        print(f"规划结果: {'✓ 成功' if success else '✗ 失败'}")
        print(f"路径安全: {'✓ 安全' if path_safe else '✗ 不安全'}")
        print(f"路径长度: {info['path_length']:.2f}")
        print(f"计算时间: {info['computation_time']:.3f}秒")
        print(f"迭代次数: {info['iterations']}")
        print(f"碰撞避免次数: {info['stats']['collision_avoidance_count']}")
        print(f"逃逸尝试次数: {info['stats']['escape_attempts']}")
    
    # 生成可视化结果
    print("\n生成可视化结果...")
    
    # 创建对比图
    n_cases = len(results)
    if n_cases > 0:
        cols = min(3, n_cases)
        rows = (n_cases + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 6*rows))
        if n_cases == 1:
            axes = [axes]
        elif rows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()
        
        fig.suptitle('Robust APF Path Planning Results', fontsize=16)
        
        for i, result in enumerate(results):
            ax = axes[i]
            ax.set_xlim(0, 100)
            ax.set_ylim(0, 100)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            
            # 绘制障碍物
            for obstacle in env.obstacles:
                if hasattr(obstacle, 'center'):  # CircleObstacle
                    circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                      obstacle.radius, color='red', alpha=0.7)
                    ax.add_patch(circle)
                    # 绘制安全边距
                    safety_circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                             obstacle.radius + config.safety_margin, 
                                             color='red', alpha=0.2, linestyle='--', fill=False)
                    ax.add_patch(safety_circle)
                else:  # PolygonObstacle
                    vertices = [(v.x, v.y) for v in obstacle.vertices]
                    polygon = plt.Polygon(vertices, color='red', alpha=0.7)
                    ax.add_patch(polygon)
            
            # 绘制路径
            start, goal = result['start'], result['goal']
            ax.plot(start.x, start.y, 'go', markersize=10, label='Start')
            ax.plot(goal.x, goal.y, 'ro', markersize=10, label='Goal')
            
            if result['success'] and len(result['path']) > 1:
                path_x = [p.x for p in result['path']]
                path_y = [p.y for p in result['path']]
                
                # 根据路径安全性选择颜色
                color = 'green' if result['path_safe'] else 'orange'
                ax.plot(path_x, path_y, color=color, linewidth=2, label='Path')
                ax.plot(path_x, path_y, 'o', color=color, markersize=2, alpha=0.6)
            
            # 设置标题
            status = "Success" if result['success'] else "Failed"
            safety = "Safe" if result['path_safe'] else "Unsafe"
            color = 'green' if (result['success'] and result['path_safe']) else 'red'
            
            ax.set_title(f"Case {result['case']}: {result['description']}\n"
                        f"{status} & {safety}\n"
                        f"Length: {result['path_length']:.1f}, "
                        f"Time: {result['computation_time']:.3f}s\n"
                        f"Collisions: {result['stats']['collision_avoidance_count']}, "
                        f"Escapes: {result['stats']['escape_attempts']}", 
                        color=color, fontsize=10)
            
            if i == 0:
                ax.legend()
        
        # 隐藏多余的子图
        for i in range(n_cases, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('robust_apf_results.png', dpi=150, bbox_inches='tight')
        print("结果已保存为 robust_apf_results.png")
    
    # 统计总结
    print("\n" + "=" * 40)
    print("测试总结")
    print("=" * 40)
    
    if results:
        success_count = sum(1 for r in results if r['success'])
        safe_count = sum(1 for r in results if r['path_safe'])
        
        avg_length = np.mean([r['path_length'] for r in results if r['success']])
        avg_time = np.mean([r['computation_time'] for r in results])
        avg_iterations = np.mean([r['iterations'] for r in results])
        
        total_collisions = sum(r['stats']['collision_avoidance_count'] for r in results)
        total_escapes = sum(r['stats']['escape_attempts'] for r in results)
        
        print(f"测试案例总数: {len(results)}")
        print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        print(f"安全率: {safe_count}/{len(results)} ({safe_count/len(results)*100:.1f}%)")
        print(f"平均路径长度: {avg_length:.2f}")
        print(f"平均计算时间: {avg_time:.3f}秒")
        print(f"平均迭代次数: {avg_iterations:.1f}")
        print(f"总碰撞避免次数: {total_collisions}")
        print(f"总逃逸尝试次数: {total_escapes}")
        
        # 检查是否解决了问题
        all_safe = all(r['path_safe'] for r in results if r['success'])
        print(f"\n问题修复状态:")
        print(f"✓ 穿越障碍物问题: {'已修复' if all_safe else '仍存在'}")
        print(f"✓ 往返折线问题: {'已改善' if total_escapes < len(results) * 5 else '需进一步优化'}")
    
    print("\n" + "=" * 60)
    print("鲁棒APF测试完成！")
    print("=" * 60)
    
    return results

if __name__ == "__main__":
    # 设置matplotlib
    plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
    
    # 运行测试
    results = test_robust_apf()
