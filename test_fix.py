"""
简化测试，验证APF修复效果
"""

import numpy as np
import matplotlib.pyplot as plt
from robust_apf import RobustAPFPlanner, RobustAPFConfig
from apf_planner import Point
from environment import Environment

def simple_fix_test():
    """简化的修复测试"""
    print("开始APF修复效果测试...")
    
    # 创建简单环境
    env = Environment(100, 100)
    
    # 添加几个关键障碍物
    env.add_circle_obstacle(Point(30, 30), 8)
    env.add_circle_obstacle(Point(70, 70), 6)
    env.add_circle_obstacle(Point(50, 50), 5)
    
    print(f"环境创建完成，包含 {len(env.obstacles)} 个障碍物")
    
    # 创建鲁棒规划器
    config = RobustAPFConfig(
        k_att=1.0,
        k_rep=200.0,
        rho_0=15.0,
        step_size=0.2,
        safety_margin=2.0,
        max_iterations=2000
    )
    
    planner = RobustAPFPlanner((100, 100), config)
    planner.obstacles = env.obstacles
    
    # 测试一个具有挑战性的案例
    start = Point(10, 10)
    goal = Point(90, 90)
    
    print(f"测试路径: ({start.x}, {start.y}) -> ({goal.x}, {goal.y})")
    
    # 检查起点和终点安全性
    start_safe = planner.is_point_safe(start)
    goal_safe = planner.is_point_safe(goal)
    
    print(f"起点安全: {start_safe}")
    print(f"终点安全: {goal_safe}")
    
    if not start_safe or not goal_safe:
        print("起点或终点不安全，调整位置...")
        if not start_safe:
            start = Point(5, 5)
        if not goal_safe:
            goal = Point(95, 95)
        print(f"调整后路径: ({start.x}, {start.y}) -> ({goal.x}, {goal.y})")
    
    # 规划路径
    print("开始路径规划...")
    path, success, info = planner.plan_path(start, goal)
    
    print(f"规划结果: {'成功' if success else '失败'}")
    print(f"路径长度: {info['path_length']:.2f}")
    print(f"计算时间: {info['computation_time']:.3f}秒")
    print(f"迭代次数: {info['iterations']}")
    print(f"路径点数: {len(path)}")
    
    if 'stats' in info:
        print(f"碰撞避免次数: {info['stats']['collision_avoidance_count']}")
        print(f"逃逸尝试次数: {info['stats']['escape_attempts']}")
    
    # 验证路径安全性
    if success and len(path) > 1:
        path_safe = True
        unsafe_points = []
        
        for i, point in enumerate(path):
            if not planner.is_point_safe(point):
                path_safe = False
                unsafe_points.append(i)
        
        print(f"路径安全性: {'安全' if path_safe else '不安全'}")
        if not path_safe:
            print(f"不安全点数量: {len(unsafe_points)}")
            print(f"不安全点索引: {unsafe_points[:10]}...")  # 只显示前10个
    
    # 可视化结果
    print("生成可视化结果...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 左图：路径和障碍物
    ax1.set_xlim(0, 100)
    ax1.set_ylim(0, 100)
    ax1.set_aspect('equal')
    ax1.grid(True, alpha=0.3)
    ax1.set_title('Robust APF Path Planning Result')
    
    # 绘制障碍物
    for obstacle in env.obstacles:
        if hasattr(obstacle, 'center'):  # CircleObstacle
            # 障碍物本体
            circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                              obstacle.radius, color='red', alpha=0.7, label='Obstacle')
            ax1.add_patch(circle)
            
            # 安全边距
            safety_circle = plt.Circle((obstacle.center.x, obstacle.center.y), 
                                     obstacle.radius + config.safety_margin, 
                                     color='orange', alpha=0.3, linestyle='--', 
                                     fill=False, label='Safety Margin')
            ax1.add_patch(safety_circle)
    
    # 绘制起点和终点
    ax1.plot(start.x, start.y, 'go', markersize=12, label='Start')
    ax1.plot(goal.x, goal.y, 'ro', markersize=12, label='Goal')
    
    # 绘制路径
    if success and len(path) > 1:
        path_x = [p.x for p in path]
        path_y = [p.y for p in path]
        ax1.plot(path_x, path_y, 'b-', linewidth=2, label='Path')
        ax1.plot(path_x, path_y, 'b.', markersize=3, alpha=0.6)
    
    ax1.legend()
    
    # 右图：力的历史
    if 'force_history' in info and info['force_history']:
        ax2.plot(info['force_history'])
        ax2.set_title('Force Magnitude History')
        ax2.set_xlabel('Iteration')
        ax2.set_ylabel('Force Magnitude')
        ax2.grid(True, alpha=0.3)
    else:
        ax2.text(0.5, 0.5, 'No force history available', 
                ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('Force History (Not Available)')
    
    plt.tight_layout()
    plt.savefig('apf_fix_test_result.png', dpi=150, bbox_inches='tight')
    print("结果已保存为 apf_fix_test_result.png")
    
    # 总结
    print("\n" + "="*50)
    print("修复效果总结:")
    print("="*50)
    
    if success:
        print("✓ 路径规划成功")
        if 'path_safe' in locals() and path_safe:
            print("✓ 路径完全安全，未穿越障碍物")
        else:
            print("⚠ 路径可能仍有安全问题")
        
        if 'stats' in info:
            if info['stats']['collision_avoidance_count'] > 0:
                print(f"✓ 成功避免了 {info['stats']['collision_avoidance_count']} 次碰撞")
            
            if info['stats']['escape_attempts'] < 5:
                print("✓ 局部最小值逃逸次数较少，路径较为平滑")
            else:
                print(f"⚠ 逃逸尝试次数较多 ({info['stats']['escape_attempts']})")
    else:
        print("✗ 路径规划失败")
    
    print("="*50)
    
    return path, success, info

if __name__ == "__main__":
    # 设置matplotlib
    plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
    
    # 运行测试
    path, success, info = simple_fix_test()
    
    print(f"\n测试完成！成功: {success}")
    if success:
        print(f"路径包含 {len(path)} 个点")
        print(f"总长度: {info['path_length']:.2f}")
