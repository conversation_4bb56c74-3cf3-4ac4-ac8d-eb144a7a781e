"""
简单的APF测试程序
"""

import numpy as np
import matplotlib.pyplot as plt
from apf_planner import APFPlanner, Point
from environment import Environment

def simple_test():
    """简单测试"""
    print("开始简单APF测试...")
    
    # 创建环境
    env = Environment(100, 100)
    
    # 添加几个简单的障碍物
    env.add_circle_obstacle(Point(30, 30), 8)
    env.add_circle_obstacle(Point(70, 70), 6)
    
    # 创建规划器
    planner = APFPlanner((100, 100))
    planner.obstacles = env.obstacles
    
    # 设置起点和终点
    start = Point(10, 10)
    goal = Point(90, 90)
    
    print(f"起点: ({start.x}, {start.y})")
    print(f"终点: ({goal.x}, {goal.y})")
    print(f"障碍物数量: {len(env.obstacles)}")
    
    # 规划路径
    path, success = planner.plan_path(start, goal)
    
    # 计算路径长度
    path_length = 0
    if len(path) > 1:
        for i in range(1, len(path)):
            path_length += path[i-1].distance_to(path[i])
    
    print(f"规划结果: {'成功' if success else '失败'}")
    print(f"路径长度: {path_length:.2f}")
    print(f"路径点数: {len(path)}")
    
    # 可视化
    fig, ax = env.visualize(path, start, goal, "简单APF测试")
    plt.savefig('apf_test_result.png', dpi=150, bbox_inches='tight')
    print("结果已保存为 apf_test_result.png")
    
    return path, success

if __name__ == "__main__":
    simple_test()
