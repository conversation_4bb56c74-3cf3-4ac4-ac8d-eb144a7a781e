"""
平衡的APF算法 - 在安全性和目标导向性之间取得平衡
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
import time
from apf_planner import Point, Obstacle, CircleObstacle, PolygonObstacle
from environment import Environment


@dataclass
class BalancedAPFConfig:
    """平衡APF算法配置"""
    k_att: float = 2.5  # 增强引力
    k_rep: float = 100.0  # 适度斥力
    rho_0: float = 10.0  # 减小影响范围
    step_size: float = 0.5  # 增大步长
    max_iterations: int = 1500  # 减少最大迭代次数
    goal_threshold: float = 1.0
    
    # 安全参数
    safety_margin: float = 1.0
    collision_check_steps: int = 10
    
    # 平衡的局部最小值处理
    escape_force: float = 30.0
    stuck_threshold: float = 0.15
    stuck_count_limit: int = 8
    
    # 目标导向控制
    goal_bias_factor: float = 0.2  # 减小切线导航影响
    tangent_navigation: bool = True
    tangent_activation_threshold: float = 0.6  # 只在接近障碍物时启用
    
    # 进度监控
    progress_check_interval: int = 100
    min_progress_rate: float = 0.02
    max_stagnation_count: int = 3


class BalancedAPFPlanner:
    """平衡的APF路径规划器"""
    
    def __init__(self, map_size: Tuple[int, int] = (100, 100), config: BalancedAPFConfig = None):
        self.map_width, self.map_height = map_size
        self.obstacles: List[Obstacle] = []
        self.config = config or BalancedAPFConfig()
        
        # 统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'tangent_navigation_count': 0,
            'progress_stagnations': 0,
            'total_iterations': 0
        }
    
    def add_obstacle(self, obstacle: Obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
    
    def is_point_safe(self, point: Point) -> bool:
        """检查点是否安全"""
        if (point.x < self.config.safety_margin or 
            point.x > self.map_width - self.config.safety_margin or
            point.y < self.config.safety_margin or 
            point.y > self.map_height - self.config.safety_margin):
            return False
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(point)
            if distance < self.config.safety_margin:
                return False
        
        return True
    
    def is_path_segment_safe(self, start: Point, end: Point) -> bool:
        """检查路径段是否安全"""
        steps = self.config.collision_check_steps
        for i in range(steps + 1):
            t = i / steps
            check_point = Point(
                start.x + t * (end.x - start.x),
                start.y + t * (end.y - start.y)
            )
            if not self.is_point_safe(check_point):
                return False
        return True
    
    def strong_attractive_force(self, current: Point, goal: Point) -> np.ndarray:
        """强化的引力计算"""
        goal_vec = np.array([goal.x - current.x, goal.y - current.y])
        distance = np.linalg.norm(goal_vec)
        
        if distance < 1e-6:
            return np.array([0.0, 0.0])
        
        # 使用强化的引力，确保目标导向性
        if distance <= self.config.goal_threshold:
            force_magnitude = self.config.k_att * distance
        else:
            # 对于远距离，使用线性引力但增强系数
            force_magnitude = self.config.k_att * min(distance, 20.0)
        
        return force_magnitude * (goal_vec / distance)
    
    def controlled_repulsive_force(self, current: Point) -> np.ndarray:
        """受控的斥力计算"""
        total_force = np.array([0.0, 0.0])
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            
            if distance > self.config.rho_0:
                continue
            
            gradient = obstacle.gradient_at_point(current)
            
            if distance < self.config.safety_margin:
                if distance < 1e-6:
                    force_magnitude = self.config.k_rep * 2000
                else:
                    # 在安全边距内使用强斥力
                    force_magnitude = self.config.k_rep * (
                        (1.0/max(distance, 0.1) - 1.0/self.config.safety_margin) * 
                        (1.0/max(distance, 0.01))
                    )
            else:
                # 标准斥力，但限制强度避免过度影响
                force_magnitude = self.config.k_rep * 0.5 * (
                    (1.0/distance - 1.0/self.config.rho_0) * (1.0/(distance**2))
                )
            
            total_force += force_magnitude * gradient
        
        # 边界斥力
        boundary_force = self._boundary_repulsive_force(current)
        total_force += boundary_force
        
        return total_force
    
    def _boundary_repulsive_force(self, current: Point) -> np.ndarray:
        """边界斥力"""
        force = np.array([0.0, 0.0])
        boundary_influence = 4.0
        
        if current.x < boundary_influence:
            force[0] += self.config.k_rep * 0.1 * (1.0/max(current.x, 0.1) - 1.0/boundary_influence)
        
        if current.x > self.map_width - boundary_influence:
            dist_to_right = self.map_width - current.x
            force[0] -= self.config.k_rep * 0.1 * (1.0/max(dist_to_right, 0.1) - 1.0/boundary_influence)
        
        if current.y < boundary_influence:
            force[1] += self.config.k_rep * 0.1 * (1.0/max(current.y, 0.1) - 1.0/boundary_influence)
        
        if current.y > self.map_height - boundary_influence:
            dist_to_top = self.map_height - current.y
            force[1] -= self.config.k_rep * 0.1 * (1.0/max(dist_to_top, 0.1) - 1.0/boundary_influence)
        
        return force
    
    def selective_tangent_navigation(self, current: Point, goal: Point) -> np.ndarray:
        """选择性切线导航 - 只在必要时启用"""
        if not self.config.tangent_navigation:
            return np.array([0.0, 0.0])
        
        # 找到最近的障碍物
        min_distance = float('inf')
        closest_obstacle = None
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            if distance < min_distance:
                min_distance = distance
                closest_obstacle = obstacle
        
        # 只在非常接近障碍物时才启用切线导航
        if (closest_obstacle is None or 
            min_distance > self.config.rho_0 * self.config.tangent_activation_threshold):
            return np.array([0.0, 0.0])
        
        # 计算到目标的方向
        goal_direction = np.array([goal.x - current.x, goal.y - current.y])
        goal_direction_norm = np.linalg.norm(goal_direction)
        
        if goal_direction_norm < 1e-6:
            return np.array([0.0, 0.0])
        
        goal_unit = goal_direction / goal_direction_norm
        
        # 计算障碍物梯度
        obstacle_gradient = closest_obstacle.gradient_at_point(current)
        
        # 检查是否真的需要切线导航
        # 如果目标方向与障碍物梯度夹角很大，说明不会直接撞上，不需要切线导航
        angle_cos = np.dot(-goal_unit, obstacle_gradient)
        if angle_cos < 0.3:  # 夹角大于70度
            return np.array([0.0, 0.0])
        
        # 计算切线方向
        tangent1 = np.array([-obstacle_gradient[1], obstacle_gradient[0]])
        tangent2 = np.array([obstacle_gradient[1], -obstacle_gradient[0]])
        
        # 选择更接近目标方向的切线
        if np.dot(tangent1, goal_unit) > np.dot(tangent2, goal_unit):
            tangent_direction = tangent1
        else:
            tangent_direction = tangent2
        
        # 切线力的强度 - 减小影响
        tangent_strength = self.config.k_att * self.config.goal_bias_factor * (
            1.0 - min_distance / self.config.rho_0
        ) * 0.5  # 进一步减小切线导航的影响
        
        self.stats['tangent_navigation_count'] += 1
        return tangent_strength * tangent_direction
    
    def total_force(self, current: Point, goal: Point) -> np.ndarray:
        """计算总力"""
        att_force = self.strong_attractive_force(current, goal)
        rep_force = self.controlled_repulsive_force(current)
        tangent_force = self.selective_tangent_navigation(current, goal)
        
        return att_force + rep_force + tangent_force
    
    def plan_path(self, start: Point, goal: Point) -> Tuple[List[Point], bool, Dict]:
        """平衡的路径规划"""
        if not self.is_point_safe(start):
            return [], False, {'error': 'Start point is not safe'}
        
        if not self.is_point_safe(goal):
            return [], False, {'error': 'Goal point is not safe'}
        
        start_time = time.time()
        path = [start]
        current = Point(start.x, start.y)
        
        stuck_count = 0
        force_history = []
        
        # 进度监控
        initial_distance = start.distance_to(goal)
        last_progress_check = 0
        last_distance = initial_distance
        stagnation_count = 0
        
        # 重置统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'tangent_navigation_count': 0,
            'progress_stagnations': 0,
            'total_iterations': 0
        }
        
        for iteration in range(self.config.max_iterations):
            self.stats['total_iterations'] = iteration + 1
            
            # 检查是否到达目标
            distance_to_goal = current.distance_to(goal)
            if distance_to_goal < self.config.goal_threshold:
                path.append(goal)
                end_time = time.time()
                
                return path, True, {
                    'computation_time': end_time - start_time,
                    'path_length': self._calculate_path_length(path),
                    'iterations': iteration + 1,
                    'stats': self.stats.copy(),
                    'force_history': force_history
                }
            
            # 进度检查
            if iteration - last_progress_check >= self.config.progress_check_interval:
                progress_rate = (last_distance - distance_to_goal) / self.config.progress_check_interval
                
                if progress_rate < self.config.min_progress_rate:
                    stagnation_count += 1
                    self.stats['progress_stagnations'] += 1
                    
                    if stagnation_count >= self.config.max_stagnation_count:
                        # 进度停滞太久，提前终止
                        break
                else:
                    stagnation_count = 0
                
                last_distance = distance_to_goal
                last_progress_check = iteration
            
            # 计算总力
            force = self.total_force(current, goal)
            force_magnitude = np.linalg.norm(force)
            force_history.append(force_magnitude)
            
            # 局部最小值检测
            if force_magnitude < 1e-6 or stuck_count > self.config.stuck_count_limit:
                self.stats['escape_attempts'] += 1
                
                # 简单有效的逃逸策略
                goal_direction = np.array([goal.x - current.x, goal.y - current.y])
                goal_direction_norm = np.linalg.norm(goal_direction)
                
                if goal_direction_norm > 1e-6:
                    goal_unit = goal_direction / goal_direction_norm
                    
                    # 尝试朝目标方向的几个偏移方向
                    escape_angles = [0, np.pi/3, -np.pi/3, np.pi/2, -np.pi/2]
                    best_direction = goal_unit
                    
                    for angle in escape_angles[1:]:  # 跳过0度（直接朝目标）
                        cos_a, sin_a = np.cos(angle), np.sin(angle)
                        rotated = np.array([
                            goal_unit[0] * cos_a - goal_unit[1] * sin_a,
                            goal_unit[0] * sin_a + goal_unit[1] * cos_a
                        ])
                        
                        # 检查这个方向是否更安全
                        test_point = Point(
                            current.x + rotated[0] * 5.0,
                            current.y + rotated[1] * 5.0
                        )
                        test_point.x = np.clip(test_point.x, self.config.safety_margin, 
                                             self.map_width - self.config.safety_margin)
                        test_point.y = np.clip(test_point.y, self.config.safety_margin, 
                                             self.map_height - self.config.safety_margin)
                        
                        if self.is_point_safe(test_point):
                            best_direction = rotated
                            break
                    
                    force += self.config.escape_force * best_direction
                
                stuck_count = 0
            
            # 限制力的大小
            max_force = 40.0
            if np.linalg.norm(force) > max_force:
                force = force / np.linalg.norm(force) * max_force
            
            # 自适应步长
            adaptive_step = self.config.step_size
            if distance_to_goal < 10.0:
                adaptive_step *= max(0.4, distance_to_goal / 10.0)
            
            # 计算候选新位置
            candidate_position = np.array([current.x, current.y]) + adaptive_step * force
            
            # 边界约束
            candidate_position[0] = np.clip(candidate_position[0], 0, self.map_width)
            candidate_position[1] = np.clip(candidate_position[1], 0, self.map_height)
            
            candidate_point = Point(candidate_position[0], candidate_position[1])
            
            # 安全性检查
            if self.is_path_segment_safe(current, candidate_point):
                new_position = candidate_position
                stuck_count = 0
            else:
                self.stats['collision_avoidance_count'] += 1
                
                # 尝试更小的步长
                for step_scale in [0.5, 0.25]:
                    small_step_pos = np.array([current.x, current.y]) + adaptive_step * step_scale * force
                    small_step_pos[0] = np.clip(small_step_pos[0], 0, self.map_width)
                    small_step_pos[1] = np.clip(small_step_pos[1], 0, self.map_height)
                    small_step_point = Point(small_step_pos[0], small_step_pos[1])
                    
                    if self.is_path_segment_safe(current, small_step_point):
                        new_position = small_step_pos
                        break
                else:
                    new_position = np.array([current.x, current.y])
                    stuck_count += 1
            
            # 检查移动距离
            movement = np.linalg.norm(new_position - np.array([current.x, current.y]))
            if movement < self.config.stuck_threshold:
                stuck_count += 1
            
            current = Point(new_position[0], new_position[1])
            path.append(Point(current.x, current.y))
        
        # 规划失败
        end_time = time.time()
        return path, False, {
            'computation_time': end_time - start_time,
            'path_length': self._calculate_path_length(path),
            'iterations': self.stats['total_iterations'],
            'stats': self.stats.copy(),
            'force_history': force_history,
            'error': 'Max iterations reached or progress stagnation',
            'final_distance': current.distance_to(goal)
        }
    
    def _calculate_path_length(self, path: List[Point]) -> float:
        """计算路径长度"""
        if len(path) < 2:
            return 0.0
        
        length = 0.0
        for i in range(1, len(path)):
            length += path[i-1].distance_to(path[i])
        
        return length
