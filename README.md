# 高效人工势场法路径规划系统

一个高效、鲁棒的人工势场法（APF）路径规划实现，支持圆形和多边形障碍物，适用于100×100地图环境。

## 特性

- ✅ **多种障碍物支持**: 圆形和多边形障碍物
- ✅ **高效计算**: 使用numpy加速，包含缓存机制
- ✅ **鲁棒性强**: 局部最小值逃逸机制
- ✅ **自适应优化**: 自适应步长和路径平滑
- ✅ **完整可视化**: 势场分布、路径规划过程动画
- ✅ **性能分析**: 详细的统计信息和参数比较
- ✅ **易于使用**: 简洁的API和丰富的示例

## 安装

```bash
pip install -r requirements.txt
```

## 快速开始

### 基础使用

```python
from apf_planner import APFPlanner, Point
from environment import Environment

# 创建环境
env = Environment(100, 100)
env.create_sample_environment()

# 创建规划器
planner = APFPlanner((100, 100))
planner.obstacles = env.obstacles

# 设置起点和终点
start = Point(10, 10)
goal = Point(90, 90)

# 规划路径
path, success = planner.plan_path(start, goal)

# 可视化结果
env.visualize(path, start, goal)
```

### 高级功能

```python
from advanced_apf import AdvancedAPFPlanner, APFConfig

# 创建高级配置
config = APFConfig(
    k_att=1.0,
    k_rep=150.0,
    rho_0=10.0,
    step_size=0.4,
    adaptive_step=True,
    smooth_path=True
)

# 创建高级规划器
planner = AdvancedAPFPlanner((100, 100), config)
planner.obstacles = env.obstacles

# 高级路径规划
path, success, info = planner.plan_path_advanced(start, goal)

print(f"路径长度: {info['path_length']:.2f}")
print(f"计算时间: {info['computation_time']:.3f}秒")
```

## 文件结构

```
APF/
├── apf_planner.py      # 核心APF算法实现
├── environment.py      # 环境和障碍物管理
├── visualizer.py       # 可视化功能
├── advanced_apf.py     # 高级优化功能
├── examples.py         # 使用示例
├── main.py            # 主演示程序
├── requirements.txt   # 依赖包列表
└── README.md         # 说明文档
```

## 核心组件

### 1. APFPlanner - 基础规划器

核心的人工势场法实现，包含：
- 引力场计算
- 斥力场计算
- 梯度下降路径搜索
- 边界处理

### 2. Environment - 环境管理

地图环境和障碍物管理：
- 圆形障碍物支持
- 多边形障碍物支持
- 有效性检查
- 随机点生成

### 3. AdvancedAPFPlanner - 高级规划器

包含多种优化技术：
- 自适应步长调整
- 路径平滑处理
- 缓存机制
- 性能统计
- 自动调参

### 4. APFVisualizer - 可视化工具

丰富的可视化功能：
- 势场分布图
- 路径规划动画
- 参数比较
- 收敛性分析

## 算法参数

### 基础参数

- `k_att`: 引力系数 (默认: 1.0)
- `k_rep`: 斥力系数 (默认: 100.0)
- `rho_0`: 斥力影响范围 (默认: 10.0)
- `step_size`: 梯度下降步长 (默认: 0.5)
- `max_iterations`: 最大迭代次数 (默认: 1000)
- `goal_threshold`: 目标到达阈值 (默认: 1.0)

### 高级参数

- `adaptive_step`: 是否使用自适应步长
- `smooth_path`: 是否进行路径平滑
- `escape_force`: 局部最小值逃逸力大小
- `stuck_threshold`: 卡住检测阈值

## 使用示例

### 运行完整演示

```bash
python main.py
```

### 运行特定示例

```bash
python examples.py
```

### 创建自定义环境

```python
from environment import Environment
from apf_planner import Point

# 创建空环境
env = Environment(100, 100)

# 添加圆形障碍物
env.add_circle_obstacle(Point(30, 30), 8)

# 添加多边形障碍物
triangle = [Point(50, 20), Point(60, 40), Point(40, 40)]
env.add_polygon_obstacle(triangle)
```

### 势场可视化

```python
from visualizer import APFVisualizer

visualizer = APFVisualizer(env, planner)
fig = visualizer.visualize_potential_field(goal, resolution=50)
```

### 参数优化

```python
# 自动调参
test_cases = [(Point(10, 10), Point(90, 90))]
best_config = planner.auto_tune_parameters(env, test_cases)
```

## 性能特点

- **计算效率**: 使用numpy向量化计算，支持缓存机制
- **内存优化**: 智能缓存管理，避免内存泄漏
- **鲁棒性**: 多种局部最小值逃逸策略
- **可扩展性**: 模块化设计，易于扩展新功能

## 适用场景

- 机器人路径规划
- 无人机导航
- 游戏AI寻路
- 自动驾驶路径规划
- 教学和研究

## 注意事项

1. 对于复杂环境，建议调整斥力系数和影响范围
2. 使用自适应步长可以提高收敛速度
3. 路径平滑功能可能增加计算时间但提高路径质量
4. 缓存机制在重复规划时可以显著提高性能

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
