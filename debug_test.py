"""
调试测试，检查APF算法基本功能
"""

import sys
import numpy as np
from robust_apf import RobustAPFPlanner, RobustAPFConfig
from apf_planner import Point
from environment import Environment

def debug_test():
    """调试测试"""
    print("=== APF调试测试开始 ===", flush=True)
    
    try:
        # 创建环境
        print("1. 创建环境...", flush=True)
        env = Environment(100, 100)
        env.add_circle_obstacle(Point(50, 50), 10)
        print(f"   环境创建成功，障碍物数量: {len(env.obstacles)}", flush=True)
        
        # 创建规划器
        print("2. 创建规划器...", flush=True)
        config = RobustAPFConfig(
            k_att=1.0,
            k_rep=100.0,
            rho_0=15.0,
            step_size=0.3,
            safety_margin=2.0,
            max_iterations=500
        )
        planner = RobustAPFPlanner((100, 100), config)
        planner.obstacles = env.obstacles
        print("   规划器创建成功", flush=True)
        
        # 设置起点和终点
        print("3. 设置起点和终点...", flush=True)
        start = Point(10, 10)
        goal = Point(90, 90)
        
        start_safe = planner.is_point_safe(start)
        goal_safe = planner.is_point_safe(goal)
        print(f"   起点 ({start.x}, {start.y}) 安全: {start_safe}", flush=True)
        print(f"   终点 ({goal.x}, {goal.y}) 安全: {goal_safe}", flush=True)
        
        # 规划路径
        print("4. 开始路径规划...", flush=True)
        path, success, info = planner.plan_path(start, goal)
        
        print(f"5. 规划完成!", flush=True)
        print(f"   成功: {success}", flush=True)
        print(f"   路径点数: {len(path)}", flush=True)
        print(f"   路径长度: {info['path_length']:.2f}", flush=True)
        print(f"   计算时间: {info['computation_time']:.3f}秒", flush=True)
        print(f"   迭代次数: {info['iterations']}", flush=True)
        
        if 'stats' in info:
            stats = info['stats']
            print(f"   碰撞避免: {stats['collision_avoidance_count']}", flush=True)
            print(f"   逃逸尝试: {stats['escape_attempts']}", flush=True)
        
        # 检查路径安全性
        print("6. 检查路径安全性...", flush=True)
        if success and len(path) > 1:
            unsafe_count = 0
            for point in path:
                if not planner.is_point_safe(point):
                    unsafe_count += 1
            
            print(f"   不安全点数量: {unsafe_count}/{len(path)}", flush=True)
            print(f"   路径安全率: {(len(path)-unsafe_count)/len(path)*100:.1f}%", flush=True)
        
        print("=== 调试测试完成 ===", flush=True)
        return success
        
    except Exception as e:
        print(f"错误: {e}", flush=True)
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_test()
    print(f"最终结果: {'成功' if success else '失败'}", flush=True)
