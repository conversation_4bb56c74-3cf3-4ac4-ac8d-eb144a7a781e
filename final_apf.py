"""
最终优化的APF算法，解决复杂反对角线路径问题
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
import time
from apf_planner import Point, Obstacle, CircleObstacle, PolygonObstacle
from environment import Environment


@dataclass
class FinalAPFConfig:
    """最终APF算法配置"""
    k_att: float = 2.0  # 增强引力
    k_rep: float = 120.0  # 适度斥力
    rho_0: float = 12.0
    step_size: float = 0.4  # 增大步长提高效率
    max_iterations: int = 2500
    goal_threshold: float = 1.0
    
    # 安全参数
    safety_margin: float = 1.2
    collision_check_steps: int = 12
    
    # 改进的局部最小值处理
    escape_force: float = 25.0
    stuck_threshold: float = 0.1
    stuck_count_limit: int = 6
    
    # 自适应参数
    adaptive_attraction: bool = True
    distance_scaling: bool = True
    
    # 路径质量控制
    max_force_magnitude: float = 35.0
    min_movement: float = 0.03
    
    # 目标导向改进
    goal_bias_factor: float = 0.4
    tangent_navigation: bool = True
    
    # 新增：进度检测和路径记忆
    progress_check_interval: int = 50
    progress_threshold: float = 2.0
    waypoint_guidance: bool = True


class FinalAPFPlanner:
    """最终优化的APF路径规划器"""
    
    def __init__(self, map_size: Tuple[int, int] = (100, 100), config: FinalAPFConfig = None):
        self.map_width, self.map_height = map_size
        self.obstacles: List[Obstacle] = []
        self.config = config or FinalAPFConfig()
        
        # 统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'tangent_navigation_count': 0,
            'waypoint_guidance_count': 0,
            'progress_resets': 0,
            'total_iterations': 0
        }
    
    def add_obstacle(self, obstacle: Obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
    
    def is_point_safe(self, point: Point) -> bool:
        """检查点是否安全"""
        if (point.x < self.config.safety_margin or 
            point.x > self.map_width - self.config.safety_margin or
            point.y < self.config.safety_margin or 
            point.y > self.map_height - self.config.safety_margin):
            return False
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(point)
            if distance < self.config.safety_margin:
                return False
        
        return True
    
    def is_path_segment_safe(self, start: Point, end: Point) -> bool:
        """检查路径段是否安全"""
        steps = self.config.collision_check_steps
        for i in range(steps + 1):
            t = i / steps
            check_point = Point(
                start.x + t * (end.x - start.x),
                start.y + t * (end.y - start.y)
            )
            if not self.is_point_safe(check_point):
                return False
        return True
    
    def generate_waypoints(self, start: Point, goal: Point) -> List[Point]:
        """生成中间路径点，帮助复杂路径规划"""
        if not self.config.waypoint_guidance:
            return []
        
        waypoints = []
        
        # 计算直线距离和方向
        direct_distance = start.distance_to(goal)
        if direct_distance < 30:  # 距离较近时不需要路径点
            return waypoints
        
        # 尝试生成几个候选路径点
        mid_x = (start.x + goal.x) / 2
        mid_y = (start.y + goal.y) / 2
        
        # 候选中间点
        candidates = [
            Point(mid_x, mid_y),  # 中点
            Point(mid_x + 15, mid_y),  # 右偏移
            Point(mid_x - 15, mid_y),  # 左偏移
            Point(mid_x, mid_y + 15),  # 上偏移
            Point(mid_x, mid_y - 15),  # 下偏移
            Point(start.x + (goal.x - start.x) * 0.3, start.y + (goal.y - start.y) * 0.7),  # 偏向终点
            Point(start.x + (goal.x - start.x) * 0.7, start.y + (goal.y - start.y) * 0.3),  # 偏向起点
        ]
        
        # 选择安全且有助于路径的点
        for candidate in candidates:
            # 确保在边界内
            candidate.x = np.clip(candidate.x, self.config.safety_margin * 2, 
                                self.map_width - self.config.safety_margin * 2)
            candidate.y = np.clip(candidate.y, self.config.safety_margin * 2, 
                                self.map_height - self.config.safety_margin * 2)
            
            if (self.is_point_safe(candidate) and 
                self.is_path_segment_safe(start, candidate) and
                self.is_path_segment_safe(candidate, goal)):
                waypoints.append(candidate)
                break  # 只需要一个好的路径点
        
        return waypoints
    
    def adaptive_attractive_force(self, current: Point, goal: Point, waypoints: List[Point] = None) -> np.ndarray:
        """自适应引力计算，支持路径点导向"""
        # 确定当前目标（可能是路径点或最终目标）
        current_target = goal
        
        if waypoints and self.config.waypoint_guidance:
            # 检查是否应该先导向路径点
            for waypoint in waypoints:
                if current.distance_to(waypoint) > 3.0:  # 还没到达路径点
                    current_target = waypoint
                    self.stats['waypoint_guidance_count'] += 1
                    break
        
        goal_vec = np.array([current_target.x - current.x, current_target.y - current.y])
        distance = np.linalg.norm(goal_vec)
        
        if distance < 1e-6:
            return np.array([0.0, 0.0])
        
        # 自适应引力强度
        if self.config.adaptive_attraction:
            min_obstacle_distance = float('inf')
            for obstacle in self.obstacles:
                obs_dist = obstacle.distance_to_point(current)
                min_obstacle_distance = min(min_obstacle_distance, obs_dist)
            
            if min_obstacle_distance < self.config.rho_0:
                attraction_boost = 1.0 + (self.config.rho_0 - min_obstacle_distance) / self.config.rho_0 * 0.5
                k_att_adaptive = self.config.k_att * attraction_boost
            else:
                k_att_adaptive = self.config.k_att
        else:
            k_att_adaptive = self.config.k_att
        
        # 距离缩放
        if self.config.distance_scaling:
            if distance <= self.config.goal_threshold:
                force_magnitude = k_att_adaptive * distance
            elif distance <= 25.0:
                force_magnitude = k_att_adaptive * self.config.goal_threshold
            else:
                # 对于很远的距离，使用对数缩放
                force_magnitude = k_att_adaptive * self.config.goal_threshold * np.log(25.0) / np.log(distance)
        else:
            force_magnitude = k_att_adaptive * min(distance, self.config.goal_threshold)
        
        return force_magnitude * (goal_vec / distance)
    
    def improved_repulsive_force(self, current: Point) -> np.ndarray:
        """改进的斥力计算"""
        total_force = np.array([0.0, 0.0])
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            
            if distance > self.config.rho_0:
                continue
            
            gradient = obstacle.gradient_at_point(current)
            
            if distance < self.config.safety_margin:
                if distance < 1e-6:
                    force_magnitude = self.config.k_rep * 3000
                else:
                    # 使用平滑的斥力函数
                    force_magnitude = self.config.k_rep * (
                        (1.0/max(distance, 0.1) - 1.0/self.config.safety_margin) * 
                        (1.0/max(distance**1.2, 0.01))
                    )
            else:
                # 标准斥力
                force_magnitude = self.config.k_rep * (
                    (1.0/distance - 1.0/self.config.rho_0) * (1.0/(distance**2))
                )
            
            total_force += force_magnitude * gradient
        
        # 边界斥力
        boundary_force = self._boundary_repulsive_force(current)
        total_force += boundary_force
        
        return total_force
    
    def _boundary_repulsive_force(self, current: Point) -> np.ndarray:
        """边界斥力"""
        force = np.array([0.0, 0.0])
        boundary_influence = 5.0
        
        if current.x < boundary_influence:
            force[0] += self.config.k_rep * 0.1 * (1.0/max(current.x, 0.1) - 1.0/boundary_influence)
        
        if current.x > self.map_width - boundary_influence:
            dist_to_right = self.map_width - current.x
            force[0] -= self.config.k_rep * 0.1 * (1.0/max(dist_to_right, 0.1) - 1.0/boundary_influence)
        
        if current.y < boundary_influence:
            force[1] += self.config.k_rep * 0.1 * (1.0/max(current.y, 0.1) - 1.0/boundary_influence)
        
        if current.y > self.map_height - boundary_influence:
            dist_to_top = self.map_height - current.y
            force[1] -= self.config.k_rep * 0.1 * (1.0/max(dist_to_top, 0.1) - 1.0/boundary_influence)
        
        return force
    
    def tangent_navigation_force(self, current: Point, goal: Point) -> np.ndarray:
        """改进的切线导航力"""
        if not self.config.tangent_navigation:
            return np.array([0.0, 0.0])
        
        # 找到最近的障碍物
        min_distance = float('inf')
        closest_obstacle = None
        
        for obstacle in self.obstacles:
            distance = obstacle.distance_to_point(current)
            if distance < min_distance and distance < self.config.rho_0:
                min_distance = distance
                closest_obstacle = obstacle
        
        if closest_obstacle is None or min_distance > self.config.rho_0 * 0.7:
            return np.array([0.0, 0.0])
        
        # 计算到目标的方向
        goal_direction = np.array([goal.x - current.x, goal.y - current.y])
        goal_direction_norm = np.linalg.norm(goal_direction)
        
        if goal_direction_norm < 1e-6:
            return np.array([0.0, 0.0])
        
        goal_unit = goal_direction / goal_direction_norm
        
        # 计算障碍物梯度
        obstacle_gradient = closest_obstacle.gradient_at_point(current)
        
        # 计算切线方向
        tangent1 = np.array([-obstacle_gradient[1], obstacle_gradient[0]])
        tangent2 = np.array([obstacle_gradient[1], -obstacle_gradient[0]])
        
        # 选择更接近目标方向的切线
        if np.dot(tangent1, goal_unit) > np.dot(tangent2, goal_unit):
            tangent_direction = tangent1
        else:
            tangent_direction = tangent2
        
        # 切线力的强度
        tangent_strength = self.config.k_att * self.config.goal_bias_factor * (
            1.0 - min_distance / self.config.rho_0
        ) * 1.5  # 增强切线导航
        
        self.stats['tangent_navigation_count'] += 1
        return tangent_strength * tangent_direction
    
    def total_force(self, current: Point, goal: Point, waypoints: List[Point] = None) -> np.ndarray:
        """计算总力"""
        att_force = self.adaptive_attractive_force(current, goal, waypoints)
        rep_force = self.improved_repulsive_force(current)
        tangent_force = self.tangent_navigation_force(current, goal)
        
        return att_force + rep_force + tangent_force
    
    def check_progress(self, current: Point, goal: Point, initial_distance: float) -> float:
        """检查向目标的进度"""
        current_distance = current.distance_to(goal)
        progress = (initial_distance - current_distance) / initial_distance
        return progress
    
    def plan_path(self, start: Point, goal: Point) -> Tuple[List[Point], bool, Dict]:
        """最终优化的路径规划"""
        if not self.is_point_safe(start):
            return [], False, {'error': 'Start point is not safe'}
        
        if not self.is_point_safe(goal):
            return [], False, {'error': 'Goal point is not safe'}
        
        start_time = time.time()
        
        # 生成路径点
        waypoints = self.generate_waypoints(start, goal)
        
        path = [start]
        current = Point(start.x, start.y)
        
        stuck_count = 0
        prev_positions = []
        force_history = []
        
        initial_distance = start.distance_to(goal)
        last_progress_check = 0
        best_progress = 0.0
        
        # 重置统计信息
        self.stats = {
            'collision_avoidance_count': 0,
            'escape_attempts': 0,
            'tangent_navigation_count': 0,
            'waypoint_guidance_count': 0,
            'progress_resets': 0,
            'total_iterations': 0
        }
        
        for iteration in range(self.config.max_iterations):
            self.stats['total_iterations'] = iteration + 1
            
            # 检查是否到达目标
            distance_to_goal = current.distance_to(goal)
            if distance_to_goal < self.config.goal_threshold:
                path.append(goal)
                end_time = time.time()
                
                return path, True, {
                    'computation_time': end_time - start_time,
                    'path_length': self._calculate_path_length(path),
                    'iterations': iteration + 1,
                    'stats': self.stats.copy(),
                    'force_history': force_history,
                    'waypoints_used': len(waypoints)
                }
            
            # 定期检查进度
            if iteration - last_progress_check >= self.config.progress_check_interval:
                current_progress = self.check_progress(current, goal, initial_distance)
                
                if current_progress > best_progress:
                    best_progress = current_progress
                elif current_progress < best_progress - 0.1:  # 进度倒退
                    # 尝试重置到更好的位置
                    self.stats['progress_resets'] += 1
                    if len(path) > 20:
                        # 回退到之前的较好位置
                        reset_index = max(0, len(path) - 15)
                        current = Point(path[reset_index].x, path[reset_index].y)
                        path = path[:reset_index + 1]
                
                last_progress_check = iteration
            
            # 计算总力
            force = self.total_force(current, goal, waypoints)
            force_magnitude = np.linalg.norm(force)
            force_history.append(force_magnitude)
            
            # 局部最小值检测和处理
            if force_magnitude < 1e-6 or stuck_count > self.config.stuck_count_limit:
                self.stats['escape_attempts'] += 1
                
                # 智能逃逸
                goal_direction = np.array([goal.x - current.x, goal.y - current.y])
                goal_direction_norm = np.linalg.norm(goal_direction)
                
                if goal_direction_norm > 1e-6:
                    goal_unit = goal_direction / goal_direction_norm
                    
                    # 尝试多个逃逸方向
                    escape_directions = [
                        goal_unit,  # 直接朝向目标
                        np.array([-goal_unit[1], goal_unit[0]]),  # 垂直方向1
                        np.array([goal_unit[1], -goal_unit[0]]),  # 垂直方向2
                    ]
                    
                    best_escape = None
                    best_score = -1
                    
                    for escape_dir in escape_directions:
                        test_point = Point(
                            current.x + escape_dir[0] * 8.0,
                            current.y + escape_dir[1] * 8.0
                        )
                        test_point.x = np.clip(test_point.x, self.config.safety_margin, 
                                             self.map_width - self.config.safety_margin)
                        test_point.y = np.clip(test_point.y, self.config.safety_margin, 
                                             self.map_height - self.config.safety_margin)
                        
                        if self.is_point_safe(test_point):
                            score = np.dot(escape_dir, goal_unit)
                            if score > best_score:
                                best_score = score
                                best_escape = escape_dir
                    
                    if best_escape is not None:
                        force += self.config.escape_force * best_escape
                    else:
                        # 随机逃逸作为最后手段
                        random_dir = np.random.normal(0, 1, 2)
                        random_dir /= np.linalg.norm(random_dir)
                        force += self.config.escape_force * random_dir
                
                stuck_count = 0
            
            # 限制力的大小
            if np.linalg.norm(force) > self.config.max_force_magnitude:
                force = force / np.linalg.norm(force) * self.config.max_force_magnitude
            
            # 自适应步长
            adaptive_step = self.config.step_size
            if distance_to_goal < 15.0:
                adaptive_step *= max(0.3, distance_to_goal / 15.0)
            
            # 计算候选新位置
            candidate_position = np.array([current.x, current.y]) + adaptive_step * force
            
            # 边界约束
            candidate_position[0] = np.clip(candidate_position[0], 0, self.map_width)
            candidate_position[1] = np.clip(candidate_position[1], 0, self.map_height)
            
            candidate_point = Point(candidate_position[0], candidate_position[1])
            
            # 安全性检查
            if self.is_path_segment_safe(current, candidate_point):
                new_position = candidate_position
                stuck_count = 0
            else:
                self.stats['collision_avoidance_count'] += 1
                
                # 尝试更小的步长
                for step_scale in [0.5, 0.25, 0.1]:
                    small_step_pos = np.array([current.x, current.y]) + adaptive_step * step_scale * force
                    small_step_pos[0] = np.clip(small_step_pos[0], 0, self.map_width)
                    small_step_pos[1] = np.clip(small_step_pos[1], 0, self.map_height)
                    small_step_point = Point(small_step_pos[0], small_step_pos[1])
                    
                    if self.is_path_segment_safe(current, small_step_point):
                        new_position = small_step_pos
                        break
                else:
                    new_position = np.array([current.x, current.y])
                    stuck_count += 1
            
            # 检查移动距离
            movement = np.linalg.norm(new_position - np.array([current.x, current.y]))
            if movement < self.config.min_movement:
                stuck_count += 1
            
            # 位置历史检测循环
            prev_positions.append(new_position.copy())
            if len(prev_positions) > 8:
                prev_positions.pop(0)
                
                if len(prev_positions) >= 6:
                    recent_positions = np.array(prev_positions[-6:])
                    if np.std(recent_positions) < 0.6:
                        stuck_count += self.config.stuck_count_limit
            
            current = Point(new_position[0], new_position[1])
            path.append(Point(current.x, current.y))
        
        # 规划失败
        end_time = time.time()
        return path, False, {
            'computation_time': end_time - start_time,
            'path_length': self._calculate_path_length(path),
            'iterations': self.config.max_iterations,
            'stats': self.stats.copy(),
            'force_history': force_history,
            'waypoints_used': len(waypoints),
            'error': 'Max iterations reached',
            'final_distance': current.distance_to(goal)
        }
    
    def _calculate_path_length(self, path: List[Point]) -> float:
        """计算路径长度"""
        if len(path) < 2:
            return 0.0
        
        length = 0.0
        for i in range(1, len(path)):
            length += path[i-1].distance_to(path[i])
        
        return length
